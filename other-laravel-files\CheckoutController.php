<?php

namespace App\Http\Controllers\frontend;

use App\Http\Controllers\Controller;
use App\Models\BranchesStock;
use App\Models\Branches;
use App\Models\Cart;
use App\Models\coupon;
use App\Models\HyperPayTransactions;
use App\Models\Orders;
use App\Models\Orders_Items;
use App\Models\Products;
use App\Models\Brands;
use App\Models\Category;
use App\Models\TabbyTransactions;
use App\Models\TamaraTransactions;
use App\Models\User;
use App\Models\AutoSale;
use App\Models\NoCodProducts;
use App\Models\Terms;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\View;
use Illuminate\Validation\Rule;
use RealRashid\SweetAlert\Facades\Alert;
use App\Http\Controllers\APIS\MsegatController;
use TheIconic\Tracking\GoogleAnalytics\Analytics;
use App\Helper\Help;

class CheckoutController extends Controller
{

    public function __construct()
    {

        if(isset($_GET["mobile"]) && $_GET["mobile"]) {
            session()->put('mobile_app', '1');
        }
    }


    public function index(){

        ////////////
        $user = Auth::user();

        $cart_items=Cart::get_cart_items();
        //dd($cart_items);
        $carts=$cart_items['carts'];
        $total=$cart_items['total'];
        $total_vat=$cart_items['total_vat'];
        $discount=$cart_items['discount'];
        $final_total=$cart_items['final_total'];
        $shipping=$cart_items['shipping'];
        $gift=$cart_items['gift'];



        //$tabbypromohidden = 'hidden';
        // $tamarapromohidden = 'hidden';
        // $tamarahidden = '';
        $nopickup = '';
        $tabbyshowroom = 0;
        $codhidden = '';
        $tabbyhidden = '';
        //$tabbyhelp = 'none';
        $visahidden = '';
        $madahidden = '';
        $appelhidden = '';
        $amexhidden = '';

        $city = $user->city;
        //if  city is not in this list  COD is not available;
        switch ($city) {
            case 'AlQatif':
                $codhidden = '';
                break;
            case 'Dammam':
                $codhidden = '';
                break;
            case 'Dhahran':
                $codhidden = '';
                break;
            case 'Hassa':
                $codhidden = '';
                break;
            case 'Jeddah':
                $codhidden = '';
                break;
            case 'Jubail':
                $codhidden = '';
                break;
            case 'Khobar':
                $codhidden = '';
                break;
            case 'Mecca':
                $codhidden = '';
                break;
            case 'Medina':
                $codhidden = '';
                break;
            case 'Rabigh':
                $codhidden = '';
                break;
            case 'Ras Tannurah':
                $codhidden = '';
                break;
            case 'Riyadh':
                $codhidden = '';
                break;
            case 'Yanbu':
                $codhidden = '';
                break;
            default:
                $codhidden = 'hidden';
        }
       //dd($codhidden);
        if ($final_total > 9999) {
            $codhidden = 'hidden';
        }
        if ($final_total < 150 || $final_total > 5000) {
            $tabbyhidden = 'hidden';
           // $tamarahidden = 'hidden';
           // $tabbyhelp = 'block';
        }

        $tabby_count=$carts->whereIn('product_id',3165)->count();
       // dd($tabby_in_cart);

        if ($tabby_count > 0) {
            //dd($tabby_count);
            $tabbyshowroom ++;
            }

        if ($tabbyshowroom > 0) {
            $visahidden = 'hidden';
            $madahidden = 'hidden';
            $codhidden = 'hidden';
            $nopickup = 'hidden';
           // $tamarapromohidden = 'hidden';
           // $tamarahidden = 'hidden';
        }
       // dd($codhidden);
        // if ($discount > 0) {
        //     $nopickup = 'hidden';
        // }


        $hide_code_complex = 0;
        //No Cod Products
        foreach ($carts as $no_cod_product) {
            $cod_product = NoCodProducts::where('product_id', '=', $no_cod_product->product_id)
                ->latest()
                ->first();
            $discounted_product_check =Products::find($no_cod_product->product_id);
            if ($cod_product) {
                $hide_code_complex ++;
            }
            // if ($discounted_product_check) {
            //     if ($discounted_product_check->sale_price > 0) {
            //         $hide_code_complex ++;
            //     }
            // }
        }

        // if ($discount > 0) {
        //     $hide_code_complex ++;
        // }

        // if ($user->isgift == 1) {
        //     $hide_code_complex ++;
        // }

        if ($hide_code_complex > 0) {

            $codhidden = 'hidden';
            // if ($user->payment == 'COD') {
            //     $user->payment = 'MadaNew';
            //     $user->save();
            // }
        }

       // dd($hide_code_complex);

        if (Terms::find(171)->value == 0) {
            $codhidden = 'hidden';
            // if ($user->payment == 'COD') {
            //     $user->payment = 'MadaNew';
            //     $user->update();
            // }
        }
       // dd($codhidden);



        /////////////
        //dd($nopickup,$tabbyshowroom,$codhidden,$tabbyhidden,$tabbyhelp,$visahidden,$madahidden,$appelhidden,$amexhidden);

        $city_branches=Cart::get_city_branches();
        $branches_stock=Cart::get_branches_for_checkout();
        $riyadh=$branches_stock['branch_stocks_riyadh'];
        $jeddah=$branches_stock['branch_stocks_jeddah'];
        $khobar=$branches_stock['branch_stocks_khobar'];

        //dd($carts);
        $cities= Terms::where('type', '=', 'ksa_city')->get();

        if(!$carts->isEmpty()){
            //return view('frontend.checkoutfirststep', compact('user','carts'));
            return view('frontend.checkoutfirststep',
             compact('carts','total','total_vat','discount','final_total','user','shipping','gift','cities',
            'nopickup','tabbyshowroom','codhidden','tabbyhidden','visahidden','madahidden','appelhidden','amexhidden',
            'riyadh','jeddah','khobar','city_branches'));
        }
        else{
            // $products = Products::all();
            // return view('frontend.index',compact('products'));
            return redirect()->route('home');
        }
    }
    public function apply_coupon_checkout(Request $request){
       // dd($request->all());
         Session::forget('couponDiscount');
         Session::forget('couponCode');
         if($request->ajax()){
             $coupon = coupon::where('title', '=', $request->code)->latest()->first();
             if($coupon){
                $coupon_details=Cart::apply_coupon($coupon);
                //dd($coupon_details);
                if(!empty(  $coupon_details )){
                    //dd($coupon_details);

                    $discounthidden = 'hidden';
                    $carts=$coupon_details['carts'];
                    $total=$coupon_details['total'];
                    $total_vat=$coupon_details['total_vat'];
                    $discount=$coupon_details['discount'];
                    $final_total=$coupon_details['final_total'];
                    $coupon_applied=$coupon_details['coupon_applied'];
                    $shipping=$coupon_details['shipping'];
                    $gift=$coupon_details['gift'];

                        if($coupon_applied){
                                    $message=__('messages.Congratulations! You have applied');

                        }else{
                                    $message= __('messages.Sorry, the used code is wrong or has been expired') ;
                        }
                        return response()->json([
                        'status'=>$coupon_applied,
                        'viewSummary'=>(String)View::make('frontend.checkout-summary')->with(compact('carts','total','total_vat','discount','final_total','shipping','gift')),
                        'message'=>$message,
                        ]);
                    }
                }
                    $message= __('messages.Sorry, the used code is wrong or has been expired') ;
                    $cart_items=Cart::get_cart_items();
                    //dd($cart_items);
                    $carts=$cart_items['carts'];
                    $total=$cart_items['total'];
                    $total_vat=$cart_items['total_vat'];
                    $discount=$cart_items['discount'];
                    $final_total=$cart_items['final_total'];
                    $shipping=$cart_items['shipping'];
                    $gift=$cart_items['gift'];

                    return response()->json([
                        'status'=>false,
                        'message'=>$message,
                        'viewSummary'=>(String)View::make('frontend.checkout-summary')->with(compact('carts','total','total_vat','discount','final_total','shipping','gift')),
                        ]);

        }

    }
    //add-gift-wrap-fees
    // public function add_gift_wrap_fees(Request $request){

    //     if($request->ajax()){
    //         Session::forget('giftWrapFees');
    //         $giftWrapFees=0;
    //         if($request->isgift=='Yes'){
    //             $giftWrapFees=35;
    //         }
    //         Session::put('giftWrapFees',$giftWrapFees);

    //         $cart_items=Cart::get_cart_items();
    //         //dd($cart_items);
    //         $carts=$cart_items['carts'];
    //         $total=$cart_items['total'];
    //         $total_vat=$cart_items['total_vat'];
    //         $discount=$cart_items['discount'];
    //         $final_total=$cart_items['final_total'];
    //         $shipping=$cart_items['shipping'];
    //         $gift=$cart_items['gift'];

    //                 //$message=__('messages.Congratulations! You have applied');
    //                 return response()->json([
    //                 'status'=>true,
    //                 'view'=>(String)View::make('frontend.checkout-summary')->with(compact('carts','total','total_vat','discount','final_total','shipping','gift')),
    //             ]);
    //     }
    // }
    // public function payment_change_checkout(Request $request){

    //     if(!empty(Session::get('paymentMethod'))){
    //         $payment_method=Session::get('paymentMethod');
    //     }
    //     else{
    //         Session::put('shippingMethod',$request->shippingmethod);
    //     }
    //     if($request->ajax()){




    //     }



    // }
    public function calculate_additional_charges(Request $request){
        // $data=$request->all();
        // dd($data);
        Session::forget('giftWrapFees');
        Session::forget('shippingMethod');
        Session::forget('paymentMethod');
        $giftWrapFees= 0;
        if($request->is_gift== 1){
            $giftWrapFees= 35;
        }
        Session::put('shippingMethod',$request->shipping_method);
        Session::put('paymentMethod',$request->payment_method);
        Session::put('giftWrapFees',$giftWrapFees);

                    //calculate additional charges ==>one code to get new calaculation {get_cart_items()}
                    $cart_items=Cart::get_cart_items();
                    //dd($cart_items);
                    $carts=$cart_items['carts'];
                    $total=$cart_items['total'];
                    $total_vat=$cart_items['total_vat'];
                    $discount=$cart_items['discount'];
                    $final_total=$cart_items['final_total'];
                    $shipping=$cart_items['shipping'];
                    $gift=$cart_items['gift'];

                    return response()->json([
                        'status'=>true,
                        'viewSummary'=>(String)View::make('frontend.checkout-summary')->with(compact('carts','total','total_vat','discount','final_total','shipping','gift')),
                        ]);

    }
    public function get_branches_checkout(Request $request){


    //     $city_branches=Cart::get_city_branches();
    //     $branches_stock=Cart::get_branches_for_checkout();
    //     $riyadh=$branches_stock['branch_stocks_riyadh'];
    //     $jeddah=$branches_stock['branch_stocks_jeddah'];
    //     $khobar=$branches_stock['branch_stocks_khobar'];
    //    // dd($riyadh);

    //     //'riyadh','jeddah','khobar','city_branches',

    //     return response()->json([
    //         'status'=>true,
    //         'viewBranch'=>(String)View::make('frontend.checkout-branches')->with(compact('riyadh','jeddah','khobar','city_branches')),
    //         //'viewSummary'=>(String)View::make('frontend.checkout-summary')->with(compact('carts','total','total_vat','discount','final_total','shipping','gift')),

    //     ]);


    }
    public function update_payment_method(Request $request){

        $data= $request->all();
        //dd($data);
        //Session::forget('paymentMethod');
        $payment_method=$data['payment_method'];
        $shipping_method=$data['shipping_method'];

        Session::put('paymentMethod',$payment_method);
        Session::put('shippingMethod',$shipping_method);
        return response()->json([
            'payment_method'=>$payment_method,
            'shipping_method'=>$shipping_method,
        ]);

                // $codhidden = '';
                // $tabbyhidden = '';
                // $tabbyhelp = 'none';
                // $tabbypromohidden = 'hidden';
                // $tamarapromohidden = 'hidden';
                // $tamarahidden = '';
                // $visahidden = '';
                // $madahidden = '';
                // $appelhidden = '';
                // $amexhidden = '';

                // $city = $user->city;
                // //if  sity no in this list  cod not available;
                // switch ($city) {
                //     case 'AlQatif':
                //         $codhidden = '';
                //         break;

                //     case 'Dammam':
                //         $codhidden = '';
                //         break;

                //     case 'Dhahran':
                //         $codhidden = '';
                //         break;

                //     case 'Hassa':
                //         $codhidden = '';
                //         break;

                //     case 'Jeddah':
                //         $codhidden = '';
                //         break;

                //     case 'Jubail':
                //         $codhidden = '';
                //         break;

                //     case 'Khobar':
                //         $codhidden = '';
                //         break;

                //     case 'Mecca':
                //         $codhidden = '';
                //         break;

                //     case 'Medina':
                //         $codhidden = '';
                //         break;

                //     case 'Rabigh':
                //         $codhidden = '';
                //         break;

                //     case 'Ras Tannurah':
                //         $codhidden = '';
                //         break;

                //     case 'Riyadh':
                //         $codhidden = '';
                //         break;

                //     case 'Yanbu':
                //         $codhidden = '';
                //         break;

                //     default:
                //         $codhidden = 'hidden';
                // }

                // if ($user->payment == 'Tabby') {
                //     $tabbypromohidden = '';
                // }
                // if ($user->payment == 'Tamara') {
                //     $tamarapromohidden = '';
                // }
                // if ($total + $shipping > 9999) {
                //     $codhidden = 'hidden';
                // }
                // if ($finaltotal > 5000) {
                //     $tabbyhidden = 'hidden';
                //     $tamarahidden = 'hidden';
                //     $tabbyhelp = 'block';
                // }
                // if ($finaltotal < 150) {
                //     $tabbyhidden = 'hidden';
                //     $tamarahidden = 'hidden';
                //     $tabbyhelp = 'block';
                // }

                // if ($tabbyshowroom > 0) {
                //     $visahidden = 'hidden';
                //     $madahidden = 'hidden';
                //     $codhidden = 'hidden';
                //     $tamarapromohidden = 'hidden';
                //     $tamarahidden = 'hidden';
                // }

                // $hide_code_complex = 0;
                // //No Cod Products
                // foreach ($carts as $no_cod_product) {
                //     $cod_product = \App\Models\NoCodProducts::where('product_id', '=', $no_cod_product->product_id)
                //         ->latest()
                //         ->first();
                //     $discounted_product_check = \App\Models\Products::find($no_cod_product->product_id);
                //     if ($cod_product) {
                //         $hide_code_complex = $hide_code_complex + 1;
                //     }
                //     if ($discounted_product_check->sale_price > 0) {
                //         $hide_code_complex = $hide_code_complex + 1;
                //     }
                // }

                // if ($discount > 0) {
                //     $hide_code_complex = $hide_code_complex + 1;
                // }

                // if ($user->isgift == 1) {
                //     $hide_code_complex = $hide_code_complex + 1;
                // }

                // if ($hide_code_complex > 0) {
                //     $codhidden = 'hidden';
                //     if ($user->payment == 'COD') {
                //         $user->payment = 'MadaNew';
                //         $user->save();
                //     }
                // }

                // if (\App\Models\Terms::find(171)->value == 0) {
                //     $codhidden = 'hidden';
                //     if ($user->payment == 'COD') {
                //         $user->payment = 'MadaNew';
                //         $user->save();
                //     }
                // }


    }

    public function test_checkout(){
        $user = Auth::user();
        $carts = Cart::where('user_id', '=', $user->id)->get();
        if(!$carts->isEmpty()){
            return view('frontend.test_checkout', compact('user','carts'));
        }
        else{
            return redirect()->route('home');
        }
    }

    public function guestIndex(){

        $user = (object) [
            'name' => '',
            'email' => '',
            'mobile' => '',
            'city' => 'Riyadh',
            'address' => '',
            'gender' => 'Male',
            'payment' => 'MadaNew',
            'isgift' => '0',
            'giftmessage' => '',
            'notes' => '',
        ];

        if(session()->has('name')){
            $user->name = session()->get('name');;
        }
        if(session()->has('email')){
            $user->email = session()->get('email');;
        }
        if(session()->has('mobile')){
            $user->mobile = session()->get('mobile');;
        }
        if(session()->has('city')){
            $user->city = session()->get('city');;
        }
        if(session()->has('address')){
            $user->address = session()->get('address');;
        }
        if(session()->has('gender')){
            $user->gender = session()->get('gender');;
        }
        if(session()->has('giftmessage')){
            $user->giftmessage = session()->get('giftmessage');;
        }
        if(session()->has('notes')){
            $user->notes = session()->get('notes');;
        }


        if(session()->has('payment')){
            $user->payment = session()->get('payment');;
        }
        else{
            session()->put('payment', 'MadaNew');
        }
        if(session()->has('isgift')){
            $user->isgift = session()->get('isgift');
        }
        else{
            session()->put('isgift', '0');
        }

        $carts = Cart::where('session_id', '=', session()->getId())->get();
        if(!$carts->isEmpty()){
            return view('frontend.checkoutguestfirststep', compact('user','carts'));
        }
        else{
            // $products = Products::all();
            // return view('frontend.index',compact('products'));
            return redirect()->route('home');
        }
    }

    public function setcity($city){

        $user = User::find(Auth::user()->id);
        $user->city = $city;
        $user->save();

        Log::channel('mht')->info("User Name City:{$user->city}-{$user->name}");

        //set city by parameter and set payment to mada if city not in fowloing  list;

        switch ($city) {
            case "AlQatif":
            break;

            case "Dammam":
            break;

            case "Dhahran":
            break;

            case "Hassa":
            break;

            case "Jeddah":
            break;

            case "Jubail":
            break;

            case "Khobar":
            break;

            case "Mecca":
            break;

            case "Medina":
            break;

            case "Rabigh":
            break;

            case "Ras Tannurah":
            break;

            case "Riyadh":
            break;

            case "Yanbu":
            break;

            default:
                $userpayment = User::find(Auth::user()->id);
                $userpayment->payment = "Mada";
                $userpayment->save();
        }

        $success = true;
        $message = "Success!";
        return response()->json([
            'success' => $success,
            'message' => $message,
            'çity' => 'city is:'. $city
        ]);
    }
    public function guestSetCity($city){


        session()->put('city', $city);


        switch ($city) {
            case "AlQatif":
            break;

            case "Dammam":
            break;

            case "Dhahran":
            break;

            case "Hassa":
            break;

            case "Jeddah":
            break;

            case "Jubail":
            break;

            case "Khobar":
            break;

            case "Mecca":
            break;

            case "Medina":
            break;

            case "Rabigh":
            break;

            case "Ras Tannurah":
            break;

            case "Riyadh":
            break;

            case "Yanbu":
            break;

            default:
                session()->put('payment', 'MadaNew');
        }

        $success = true;
        $message = "Success!";
        return response()->json([
            'success' => $success,
            'message' => $message,
            'city'=>$city
        ]);
    }

    public function apply_discount(Request $request){
        $discountamount = coupon::where('title', '=', $request->code)->latest()->first();
        if(Auth::check()){
            $carts = Cart::where('user_id', '=', Auth::user()->id)->get();
        }
        else{
            $carts = Cart::where('session_id', '=', session()->getId())->get();
        }
        if($discountamount){
            if($discountamount->used < $discountamount->max_usage){
                // Prevent Apply Discount on Sale Products
                $noDiscountedProduct = true;
                foreach($carts as $discounted_product){
                    if(Products::find($discounted_product->product_id)->sale_price > 0){
                        $noDiscountedProduct = false;
                    }
                }
                if($noDiscountedProduct){
                    if($discountamount->type == 0 or $discountamount->type == 2){
                        session()->put('coupon', $request->code);
                        return redirect()->back();
                    }
                    //Quick Edit For Tabby Promo 1/10
                    // else if($discountamount->type == 3){
                    //     $subtotal=0;
                    //     $notOnSale = true;
                    //     foreach($carts as $discountcart){
                    //         $product=\App\Models\Products::find($discountcart->product_id);
                    //         if($product->sale_price==0){
                    //             $subtotal = $subtotal + ($product->regular_price*$discountcart->qty);
                    //         }
                    //         else{
                    //             $subtotal = $subtotal + ($product->sale_price*$discountcart->qty);
                    //             $notOnSale = false;
                    //         }
                    //     }
                    //     if($notOnSale){
                    //         if(Auth::check()){
                    //             if($subtotal > 150){
                    //                 $userpayment = User::find(Auth::user()->id);
                    //                 $userpayment->payment = "Tabby";
                    //                 $userpayment->save();
                    //                 session()->put('coupon', $request->code);
                    //                 return redirect()->back();
                    //             }
                    //             else{
                    //                 return redirect()->back()->withErrors(['discount' => 'discount']);
                    //             }
                    //         }
                    //         else{
                    //             if($subtotal > 150){
                    //                 session()->put('payment', 'Tabby');
                    //                 session()->put('coupon', $request->code);
                    //                 return redirect()->back();
                    //             }
                    //             else{
                    //                 return redirect()->back()->withErrors(['discount' => 'discount']);
                    //             }
                    //         }
                    //     }
                    //     else{
                    //         return redirect()->back()->withErrors(['discount' => 'discount']);
                    //     }
                    // }
                    else if($discountamount->type == 1){
                        $product_discount_found=false;
                        foreach($carts as $discount_product){
                            if($discount_product->product_id == $discountamount->product_id){
                                $product_discount_found = true;
                            }
                            else{
                                continue;
                            }
                        }
                        if($product_discount_found){
                            session()->put('coupon', $request->code);
                            return redirect()->back();
                        }
                        else{
                            return redirect()->back()->withErrors(['discount' => 'discount']);
                        }
                    }
                }
                else{
                    return redirect()->back()->withErrors(['discount' => 'discount']);
                }

            }
            else{
                return redirect()->back()->withErrors(['discount' => 'discount']);
            }
        }
        else{
            return redirect()->back()->withErrors(['discount' => 'discount']);
        }
    }
    public function remove_discount(){
        session()->forget('coupon');
        return redirect()->back();

    }
    public function removeCheckoutCartItem(Request $request){
        Session::forget('couponDiscount');
        Session::forget('couponCode');
            if($request->ajax()){
                        $user = Auth::user();
                        if(Auth::check()){
                            $cart = Cart::where('user_id', '=', $user->id)->where('id', '=', $request->cart_id)->first();
                        }
                        else{
                            $cart = Cart::where('session_id', '=', session()->getId())->where('id', '=', $request->cart_id)->first();
                        }
                               if($cart){
                                    $cart->delete();
                                    $cart_items=Cart::get_cart_items();

                                    $carts=$cart_items['carts'];
                                   // dd($carts);
                                    if($carts->count() ==0){

                                        $message=__('alerts.removefromcartwarning');
                                        return response()->json([
                                            'status'=>false,
                                            'message'=>$message,
                                        ]);

                                    }
                                    $total=$cart_items['total'];
                                    $total_vat=$cart_items['total_vat'];
                                    $discount=$cart_items['discount'];
                                    $final_total=$cart_items['final_total'];
                                    $shipping=$cart_items['shipping'];
                                    $gift=$cart_items['gift'];


                                    //get branches because item has changed
                                        $city_branches=Cart::get_city_branches();
                                        $branches_stock=Cart::get_branches_for_checkout();
                                        $riyadh=$branches_stock['branch_stocks_riyadh'];
                                        $jeddah=$branches_stock['branch_stocks_jeddah'];
                                        $khobar=$branches_stock['branch_stocks_khobar'];
                                    //end branches

                                    $message=__('alerts.removefromcart') ;

                                    return response()->json([
                                        'status'=>true,
                                        'message'=>$message,
                                        'viewCart'=>(String)View::make('frontend.mini-cart')->with(compact('carts','total','total_vat','discount','final_total')),
                                        'viewSummary'=>(String)View::make('frontend.checkout-summary')->with(compact('carts','total','total_vat','discount','final_total','shipping','gift')),
                                        'viewBranch'=>(String)View::make('frontend.checkout-branches')->with(compact('riyadh','jeddah','khobar','city_branches')),
                                    ]);
                               }else{
                                //toast(__('alerts.cartqtyupdatewarning') ,'warning');

                                $message=__('alerts.removefromcartwarning');
                                return response()->json([
                                    'status'=>false,
                                    'message'=>$message,
                                ]);

                               }
                    }
    }



    public function place_order(Request $request){
        $data= $request->all();
        //$payment_method=$data['paymentmethod'];

         // dd($data);
            $request->validate([
                'paymentmethod'=>'required',
                'name' => 'required',
                'address' => 'required|max:50',
                'email' => [
                    'required',
                    'email',
                ],
                'mobile' => 'required|digits:10',
                'city' => 'required|min:4',
                'city_area' => 'required'
            ],[
                'name.required' => __('alerts.nameformat'),
                'address.required' => __('alerts.addresscharacters'),
                'email.required' => __('alerts.duplicatedemail'),
                'mobile.required' => __('alerts.mobileformat'),
                'paymentmethod.required'=>__("messages.You Must Choose Payment Method"),
                'city.required' => __("messages.Choose_City"),
                'city_area.required' => __("messages.please enter city area"),
                'city.min' => __("messages.Choose_City")
            ]);

            //MHT up i force client to select payment method

            // name: eyad ahmad
            // city: Riyadh
            // address: test
            // email: <EMAIL>
            // gender: Male
            // mobile: 0501234567
            // notes:
            // giftCheck: on
            // giftcolor: Red
            // giftmessage: eyad message test
            // shippingmethod: express
            // paymentmethod: MadaNew

            $giftcolor=$data['giftcolor'];

            $user = User::find( Auth::user()->id);

            //dd($data);
            $user->name=$data['name'];
            $user->city=$data['city'];
             $user->city_area=$data['city_area'];
            $user->address=$data['address'];
            $user->email=$data['email'];
            $user->gender=$data['gender'];
            $user->mobile=$data['mobile'];
            $payment_method=$data['paymentmethod'];
            $user->payment=$payment_method;

            $shipping_method=$data['shippingmethod'];

            if(!empty($data['giftCheck'])){
                $isgift=1;
            }else{
                $isgift=0;
            }
            $user->isgift=$isgift;
            if(!empty($data['giftmessage'])){
                $giftmessage=$data['giftmessage'];
            }else{
                $giftmessage='';
            }
            if( $isgift == 0){
                $giftmessage='';
            }
            $user->giftmessage=$giftmessage;
            if(!empty($data['notes'])){
               $notes=$data['notes'];
            }else{
               $notes='';
            }
            $user->notes=$notes;
            $user->save();
            //get carts
            $cart_items=Cart::get_cart_items();
                  // dd($cart_items);
                    $carts=$cart_items['carts'];
                    $subtotal=$cart_items['total'];
                    $includingtax=$cart_items['total_vat'];
                    $discount=$cart_items['discount'];
                    $finaltotal=$cart_items['final_total'];
                    $shipping=$cart_items['shipping'];
                    $gift=$cart_items['gift'];
                    $discount_code=$cart_items['discount_code'];
           // $carts = Cart::where('user_id', '=', Auth::user()->id)->get();

            if($carts->isEmpty()){
                return redirect()->route('cart');
            }

            //Check Carts Products QTY
            if($shipping_method=="express"){
                $info=0;
                foreach($carts as $cart){
                    $product=Products::find($cart->product_id);
                    if($product->stock < $cart->qty){
                        Cart::find($cart->id)->delete();
                        $info = $info + 1;
                    }
                }
                if($info>0){
                    // return view('frontend.cart', compact('carts'));
                    Alert::error(__('alerts.outofstocktitle'), __('alerts.outofstockproduct'));
                    return redirect()->route('cart');
                }
            }

    //Pickup  MHT:if pickip edit puckup data
    $ispickup = 0;
    $city_branch = explode(",", $request->city_branch);
    $pickup_city = "nothing";
    $pickup_branch = "nothing";
    if($shipping_method=="pickup"){
        $ispickup = 1;
        $pickup_city = $city_branch[0];
        $pickup_branch = $city_branch[1];
        //$shipping = 0;
    }
   // dd($pickup_city, $pickup_branch);

   //--START Payment Section --------------------------------------
    if($payment_method == "VisaMasterNew"){
        $order_id = 0;
                Orders::create([
                    "user_id"=>$user->id,
                    "name"=>preg_replace('/[^\w\s]+/u','' , $request->name),
                    "email"=>$request->email,
                    "country"=>"ksa",
                    "city"=>$user->city,
                    "city_area"=> $user->city_area,
                    "address"=>preg_replace('/[^\w\s]+/u','' , $request->address),
                    "gender"=>$request->gender,
                    "mobile"=>$request->mobile,
                    "subtotal"=>$subtotal,
                    "tax_total"=>$includingtax,
                    "shipping_cost"=>$shipping,
                    "additional_charges"=>$gift,
                    "discount"=>$discount,
                    "total_price"=>$finaltotal,
                    "payment_method"=>$payment_method,
                    "shipping_method"=>$shipping_method,
                    "notes"=>$user->notes,
                    "status"=>"Pending",
                    "isgift"=>$user->isgift,
                    "giftcolor"=>$giftcolor,
                    "giftmessage"=>$user->giftmessage,
                    "ispickup"=>$ispickup,
                    "pickup_city"=>$pickup_city,
                    "pickup_branch"=>$pickup_branch,
                    "discount_code"=>$discount_code
                    ]);


                    // $order_info_for_tabby=   [
                    // "user_id"=>$user->id,
                    // "name"=>preg_replace('/[^\w\s]+/u','' , $request->name),
                    // "email"=>$request->email,
                    // "country"=>"ksa",
                    // "city"=>$user->city,
                    // "city_area"=> $user->city_area,
                    // "address"=>preg_replace('/[^\w\s]+/u','' , $request->address),
                    // "gender"=>$request->gender,
                    // "mobile"=>$request->mobile,
                    // "subtotal"=>$subtotal,
                    // "tax_total"=>$includingtax,
                    // "shipping_cost"=>$shipping,
                    // "additional_charges"=>$gift,
                    // "discount"=>$discount,
                    // "total_price"=>$finaltotal,
                    // "payment_method"=>$payment_method,
                    // "shipping_method"=>$shipping_method,
                    // "notes"=>$user->notes,
                    // "status"=>"Pending",
                    // "isgift"=>$user->isgift,
                    // "giftcolor"=>$giftcolor,
                    // "giftmessage"=>$user->giftmessage,
                    // "ispickup"=>$ispickup,
                    // "pickup_city"=>$pickup_city,
                    // "pickup_branch"=>$pickup_branch,
                    // "discount_code"=>$discount_code
                    // ];


            // Adding Products To Orders_Items Table & Minus Stock
            $order_id=Orders::where('user_id', '=', $user->id)->latest()->first()->id;
            foreach($carts as $cart){
                $product=Products::find($cart->product_id);
                $product_final_price=0;
                if($product->sale_price==0){
                    $product_final_price = $product->regular_price;
                }
                else{
                    $product_final_price = $product->sale_price;
                }
                Orders_Items::create([
                    "order_id"=>$order_id,
                    "product_id"=>$product->id,
                    "product_name"=>$product->title,
                    "product_qty"=>$cart->qty,
                    "product_final_price"=>$product_final_price,
                    "product_sku"=>$product->sku,
                ]);

                $product->stock = $product->stock - $cart->qty;
                $product->save();
            }

        $hyperformat = number_format($finaltotal, 2, '.', '');
        //dd($hyperformat , $order_id);
        return redirect()->route('checkout.hyperpaycardnew', ['price'=>Crypt::encrypt($hyperformat), 'order_id'=>Crypt::encrypt($order_id)]);
    }
    //mht
    else if($payment_method == "AMEX"){
        $order_id = 0;
         Orders::create([
                    "user_id"=>$user->id,
                    "name"=>preg_replace('/[^\w\s]+/u','' , $request->name),
                    "email"=>$request->email,
                    "country"=>"ksa",
                    "city"=>$user->city,
                    "city_area"=> $user->city_area,
                    "address"=>preg_replace('/[^\w\s]+/u','' , $request->address),
                    "gender"=>$request->gender,
                    "mobile"=>$request->mobile,
                    "subtotal"=>$subtotal,
                    "tax_total"=>$includingtax,
                    "shipping_cost"=>$shipping,
                    "additional_charges"=>$gift,
                    "discount"=>$discount,
                    "total_price"=>$finaltotal,
                    "payment_method"=>$payment_method,
                    "shipping_method"=>$shipping_method,
                    "notes"=>$user->notes,
                    "status"=>"Pending",
                    "isgift"=>$user->isgift,
                    "giftcolor"=>$giftcolor,
                    "giftmessage"=>$user->giftmessage,
                    "ispickup"=>$ispickup,
                    "pickup_city"=>$pickup_city,
                    "pickup_branch"=>$pickup_branch,
                    "discount_code"=>$discount_code
                    ]);

            // Adding Products To Orders_Items Table & Minus Stock
            $order_id=Orders::where('user_id', '=', $user->id)->latest()->first()->id;
            foreach($carts as $cart){
                $product=Products::find($cart->product_id);
                $product_final_price=0;
                if($product->sale_price==0){
                    $product_final_price = $product->regular_price;
                }
                else{
                    $product_final_price = $product->sale_price;
                }
                Orders_Items::create([
                    "order_id"=>$order_id,
                    "product_id"=>$product->id,
                    "product_name"=>$product->title,
                    "product_qty"=>$cart->qty,
                    "product_final_price"=>$product_final_price,
                    "product_sku"=>$product->sku,
                ]);

                $product->stock = $product->stock - $cart->qty;
                $product->save();
            }

        $hyperformat = number_format($finaltotal, 2, '.', '');
        return redirect()->route('checkout.amexCard', ['price'=>Crypt::encrypt($hyperformat), 'order_id'=>Crypt::encrypt($order_id)]);
    }
    else if($payment_method == "MadaNew"){
        //dd($payment_method);
        $order_id = 0;
                Orders::create([
                    "user_id"=>$user->id,
                    "name"=>preg_replace('/[^\w\s]+/u','' , $request->name),
                    "email"=>$request->email,
                    "country"=>"ksa",
                    "city"=>$user->city,
                    "city_area"=> $user->city_area,
                    "address"=>preg_replace('/[^\w\s]+/u','' , $request->address),
                    "gender"=>$request->gender,
                    "mobile"=>$request->mobile,
                    "subtotal"=>$subtotal,
                    "tax_total"=>$includingtax,
                    "shipping_cost"=>$shipping,
                    "additional_charges"=>$gift,
                    "discount"=>$discount,
                    "total_price"=>$finaltotal,
                    "payment_method"=>$payment_method,
                    "shipping_method"=>$shipping_method,
                    "notes"=>$user->notes,
                    "status"=>"Pending",
                    "isgift"=>$user->isgift,
                    "giftcolor"=>$giftcolor,
                    "giftmessage"=>$user->giftmessage,
                    "ispickup"=>$ispickup,
                    "pickup_city"=>$pickup_city,
                    "pickup_branch"=>$pickup_branch,
                    "discount_code"=>$discount_code
                    ]);

            // Adding Products To Orders_Items Table & Minus Stock
            $order_id=Orders::where('user_id', '=', $user->id)->latest()->first()->id;
            foreach($carts as $cart){
                $product=Products::find($cart->product_id);
                $product_final_price=0;
                if($product->sale_price==0){
                    $product_final_price = $product->regular_price;
                }
                else{
                    $product_final_price = $product->sale_price;
                }
                Orders_Items::create([
                    "order_id"=>$order_id,
                    "product_id"=>$product->id,
                    "product_name"=>$product->title,
                    "product_qty"=>$cart->qty,
                    "product_final_price"=>$product_final_price,
                    "product_sku"=>$product->sku,
                ]);

                $product->stock = $product->stock - $cart->qty;
                $product->save();
            }
        $hyperformat = number_format($finaltotal, 2, '.', '');
        return redirect()->route('checkout.hyperpaymadacardnew', ['price'=>Crypt::encrypt($hyperformat), 'order_id'=>Crypt::encrypt($order_id)]);
    }
    //mht
    else if($payment_method == "ApplePayNew"){
        //dd($request->all());
        $order_id = 0;
                Orders::create([
                    "user_id"=>$user->id,
                    "name"=>preg_replace('/[^\w\s]+/u','' , $request->name),
                    "email"=>$request->email,
                    "country"=>"ksa",
                    "city"=>$user->city,
                    "city_area"=> $user->city_area,
                    "address"=>preg_replace('/[^\w\s]+/u','' , $request->address),
                    "gender"=>$request->gender,
                    "mobile"=>$request->mobile,
                    "subtotal"=>$subtotal,
                    "tax_total"=>$includingtax,
                    "shipping_cost"=>$shipping,
                    "additional_charges"=>$gift,
                    "discount"=>$discount,
                    "total_price"=>$finaltotal,
                    "payment_method"=>$payment_method,
                    "shipping_method"=>$shipping_method,
                    "notes"=>$user->notes,
                    "status"=>"Pending",
                    "isgift"=>$user->isgift,
                    "giftcolor"=>$giftcolor,
                    "giftmessage"=>$user->giftmessage,
                    "ispickup"=>$ispickup,
                    "pickup_city"=>$pickup_city,
                    "pickup_branch"=>$pickup_branch,
                    "discount_code"=>$discount_code
                    ]);

            // Adding Products To Orders_Items Table & Minus Stock
            $order_id=Orders::where('user_id', '=', $user->id)->latest()->first()->id;
            foreach($carts as $cart){
                $product=Products::find($cart->product_id);
                $product_final_price=0;
                if($product->sale_price==0){
                    $product_final_price = $product->regular_price;
                }
                else{
                    $product_final_price = $product->sale_price;
                }
                Orders_Items::create([
                    "order_id"=>$order_id,
                    "product_id"=>$product->id,
                    "product_name"=>$product->title,
                    "product_qty"=>$cart->qty,
                    "product_final_price"=>$product_final_price,
                    "product_sku"=>$product->sku,
                ]);

                $product->stock = $product->stock - $cart->qty;
                $product->save();
            }

        $hyperformat = number_format($finaltotal, 2, '.', '');
        return redirect()->route('checkout.hyperAppelCardNew', ['price'=>Crypt::encrypt($hyperformat), 'order_id'=>Crypt::encrypt($order_id)]);
    }
    else if($payment_method == "Tabby"){
        $hyperformat = number_format($finaltotal, 2, '.', '');
        return redirect()->route('checkout.tabbycard', ['price'=>Crypt::encrypt($hyperformat), 'user_id'=>$user->id,'subtotal'=>Crypt::encrypt($subtotal), 'shipping'=>$shipping, 'includingtax'=>$includingtax, 'discount'=>$discount,'isgift'=>$isgift, 'giftcolor'=>$giftcolor,'ispickup'=>$ispickup, 'pickup_city'=>$pickup_city, 'pickup_branch'=>$pickup_branch]);
        //$this->tabbycard(Crypt::encrypt($hyperformat),$user->id,Crypt::encrypt($subtotal),$shipping, $payment_method,$shipping_method,$gift,$discount_code,$includingtax, $discount,$isgift, $giftcolor,$ispickup,$pickup_city,$pickup_branch);
    }
     // else  payment at delivery COD
   else if($payment_method == "COD"){
        //Quick Edit For Big Sale 16-11-2022
        // return redirect()->back();
        //Check if COD is correct
        $hide_code_complex = 0;
        foreach($carts as $no_cod_product){
            $cod_product = NoCodProducts::where('product_id', '=', $no_cod_product->product_id)->latest()->first();
            $discounted_product_check = Products::find($no_cod_product->product_id);
            if($cod_product){
                $hide_code_complex = $hide_code_complex + 1;
            }
            if($discounted_product_check->sale_price > 0){
                $hide_code_complex = $hide_code_complex + 1;
            }
        }
        switch ($user->city) {
            case "AlQatif":
            break;

            case "Dammam":
            break;

            case "Dhahran":
            break;

            case "Hassa":
            break;

            case "Jeddah":
            break;

            case "Jubail":
            break;

            case "Khobar":
            break;

            case "Mecca":
            break;

            case "Medina":
            break;

            case "Rabigh":
            break;

            case "Ras Tannurah":
            break;

            case "Riyadh":
            break;

            case "Yanbu":
            break;

            default:
                $hide_code_complex = $hide_code_complex + 1;
        }
        if($shipping_method=="express"){
            if($discount > 0 or $user->isgift==1 or $hide_code_complex > 0){
                return redirect()->back()->withErrors(['discountcod' => __('alerts.discountcod')]);
            }
        }
        // Create Order
         Orders::create([
                    "user_id"=>$user->id,
                    "name"=>preg_replace('/[^\w\s]+/u','' , $request->name),
                    "email"=>$request->email,
                    "country"=>"ksa",
                    "city"=>$user->city,
                    "city_area"=> $user->city_area,
                    "address"=>preg_replace('/[^\w\s]+/u','' , $request->address),
                    "gender"=>$request->gender,
                    "mobile"=>$request->mobile,
                    "subtotal"=>$subtotal,
                    "tax_total"=>$includingtax,
                    "shipping_cost"=>$shipping,
                    "additional_charges"=>$gift,
                    "discount"=>$discount,
                    "total_price"=>$finaltotal,
                    "payment_method"=>$payment_method,
                    "shipping_method"=>$shipping_method,
                    "notes"=>$user->notes,
                    "status"=>"Processing",
                    "isgift"=>$user->isgift,
                    "giftcolor"=>$giftcolor,
                    "giftmessage"=>$user->giftmessage,
                    "ispickup"=>$ispickup,
                    "pickup_city"=>$pickup_city,
                    "pickup_branch"=>$pickup_branch,
                    "discount_code"=>$discount_code
                    ]);

            // Adding Products To Orders_Items Table & Minus Stock
            $order_id=Orders::where('user_id', '=', $user->id)->latest()->first()->id;
            foreach($carts as $cart){
                $product=Products::find($cart->product_id);
                $product_final_price=0;
                if($product->sale_price==0){
                    $product_final_price = $product->regular_price;
                }
                else{
                    $product_final_price = $product->sale_price;
                }
                Orders_Items::create([
                    "order_id"=>$order_id,
                    "product_id"=>$product->id,
                    "product_name"=>$product->title,
                    "product_qty"=>$cart->qty,
                    "product_final_price"=>$product_final_price,
                    "product_sku"=>$product->sku,
                ]);

                $product->stock = $product->stock - $cart->qty;
                $product->save();
            }

            // Clear Cart
            Cart::where('user_id', '=', $user->id)->delete();

            // Clear Copoun Session
            if(session()->has('coupon')){
                $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
                $used_coupon->used = $used_coupon->used + 1;
                $used_coupon->save();
                session()->forget('coupon');
            }

            $user->isgift = 0;
            $user->giftmessage = "nothing";
            $user->notes = "nothing";
            $user->save();
            return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
        }
}

     //mht prepare to transaction for amex get chechout id
    public function amexCard ($price, $order_id){

     // echo "amex";

        if(Auth::check()){
            $user = User::find(Auth::user()->id);
        }
        else{
            $user = (object) [
                'id' => '26295',
                'name' => session()->get('name'),
                'email' => session()->get('email'),
                'mobile' => session()->get('mobile'),
                'address' => session()->get('address'),
                'gender' => session()->get('gender'),
                'isgift' => session()->get('isgift'),
                'payment' => session()->get('payment'),
                'city' => session()->get('city'),
            ];
        }


        $price = Crypt::decrypt($price);
        $order_id = Crypt::decrypt($order_id);
        $url = "https://eu-prod.oppwa.com/v1/checkouts";
        $data = "entityId=8ac9a4c767a15c0b0167dbf4171c6fbf".
                "&amount=$price".
                "&integrity=true".
                "&merchantTransactionId=$order_id".
                "&currency=SAR".
                "&paymentType=DB".
                "&customer.email=$user->email".
                "&billing.street1=$user->address".
                "&billing.city=$user->city".
                "&billing.state=$user->city".
                "&billing.country=SA".
                "&billing.postcode=111".
                "&customer.givenName=$user->name".
                "&customer.surname=$user->name";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Authorization:Bearer OGFjOWE0Yzc2N2ExNWMwYjAxNjdkYmYzZDRhYjZmYmJ8ekpycmNNSEY2dw=='));
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);// this should be set to true in production
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $responseData = curl_exec($ch);
        if(curl_errno($ch)) {
            return curl_error($ch);
        }
        curl_close($ch);
        $hyperpay_checkout_result = json_decode($responseData, true);

     // dd($hyperpay_checkout_result);

        if($hyperpay_checkout_result['result']['code'] == "000.200.100"){
            $checkoutId = $hyperpay_checkout_result['id'];
            $integrity=$hyperpay_checkout_result['integrity'];
            return view('frontend.amexcard', compact('checkoutId','integrity', 'order_id'));
        }
        else{
            if(Auth::check()){
                HyperPayTransactions::create([
                    "user_id"=>Auth::user()->id,
                    "total"=>$price,
                    "status"=>"ERROR",
                    "data"=>$responseData
                ]);
                return $this->failed(__('alerts.hypercheckouturl'));
            }
            else{  // else geust
                HyperPayTransactions::create([
                    "user_id"=>26295,
                    "total"=>$price,
                    "status"=>"ERROR",
                    "data"=>$responseData
                ]);
                return $this->failed(__('alerts.hypercheckouturl'));
            }
        }
    }
    public function amexPayResult($id , $order_id){
      //  return "American express Pay";

        $url = "https://eu-prod.oppwa.com/v1/checkouts/$id/payment";
        $url .= "?entityId=8ac9a4c767a15c0b0167dbf4171c6fbf";
        $ch= curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                       'Authorization:Bearer OGFjOWE0Yzc2N2ExNWMwYjAxNjdkYmYzZDRhYjZmYmJ8ekpycmNNSEY2dw=='
                       ));
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);// this should be set to true in production
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $responseData = curl_exec($ch);
        if(curl_errno($ch)) {
            return curl_error($ch);
        }
        curl_close($ch);
        $hyperpay_payment_result = json_decode($responseData, true);

        $transaction_result = $hyperpay_payment_result['result']['code'];
        $processing_codes = "/^(000\.000\.|000\.100\.1|000\.[36])/";
        $pending_codes = "/^(000\.200)/";
        $pending_v2_codes = "/^(800\.400\.5|100\.400\.500)/";

        $old_transaction = HyperPayTransactions::where('order_id', '=', $order_id)->get();
        if(!$old_transaction->isEmpty()){
            return $this->failed(__('alerts.hyperpaytransaction'));
        }
        else {
            if(preg_match($processing_codes, $transaction_result)){
                $hyperpay_order_details = Orders::find($order_id);
                $hyperpay_order_details->payment_method = 'AMEX';
                $hyperpay_order_details->status = "Processing";
                $hyperpay_order_details->save();


                // Clear Copoun Session
                if(session()->has('coupon')){
                    $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
                    $used_coupon->used = $used_coupon->used + 1;
                    $used_coupon->save();
                    session()->forget('coupon');
                }

                if(Auth::check()){
                    $user = User::find(Auth::user()->id);
                    // Clear Cart
                    Cart::where('user_id', '=', $user->id)->delete();
                    $user->isgift = 0;
                    $user->giftmessage = "nothing";
                    $user->notes = "nothing";
                    $user->save();
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>$user->id,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Processing",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                }
                else{
                    Cart::where('session_id', '=', session()->getId())->delete();
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>26295,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Processing",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.guestconfirmed', ['id'=>$order_id]);
                }
            }
            else if(preg_match($pending_codes, $transaction_result) || preg_match($pending_v2_codes, $transaction_result)){
                $hyperpay_order_details = Orders::find($order_id);
                $hyperpay_order_details->payment_method = 'AMEX';
                $hyperpay_order_details->status = "Pending";
                $hyperpay_order_details->save();

                // Clear Copoun Session
                if(session()->has('coupon')){
                    $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
                    $used_coupon->used = $used_coupon->used + 1;
                    $used_coupon->save();
                    session()->forget('coupon');
                }
                if(Auth::check()){
                    $user = User::find(Auth::user()->id);
                    // Clear Cart
                    Cart::where('user_id', '=', $user->id)->delete();
                    $user->isgift = 0;
                    $user->giftmessage = "nothing";
                    $user->notes = "nothing";
                    $user->save();

                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>$user->id,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Pending",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                }
                else{
                    Cart::where('session_id', '=', session()->getId())->delete();
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>26295,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Pending",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                }
            }
            else{
                $hyperpay_order_details = Orders::find($order_id);
                $hyperpay_order_details->payment_method = 'AMEX';
                $hyperpay_order_details->status = "Rejected";
                $hyperpay_order_details->save();

                //RESTOCK Products
                $pending_products = Orders_Items::where('order_id', '=', $order_id)->get();
                foreach($pending_products as $item){
                    $restock_Product = Products::find($item->product_id);
                    $restock_Product->stock = $restock_Product->stock + $item->product_qty;
                    $restock_Product->save();
                }
                if(Auth::check()){
                    $user = User::find(Auth::user()->id);
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>$user->id,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"REJECTED",
                        "data"=>$responseData,
                    ]);
                    return $this->failed(__('alerts.Your payment has beed REJECTED from the gateway'));
                }
                else{
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>26295,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"REJECTED",
                        "data"=>$responseData,
                    ]);
                    return $this->failed(__('alerts.Your payment has beed REJECTED from the gateway'));

                }

            }
        }
    }

   //mht prepare to transaction for appel
   public function hyperAppelCard($price, $order_id){

    // echo "<h2>Payment</h2>";

     if(Auth::check()){
         $user = User::find(Auth::user()->id);
     }
     else{
         $user = (object) [
             'id' => '26295',
             'name' => session()->get('name'),
             'email' => session()->get('email'),
             'mobile' => session()->get('mobile'),
             'address' => session()->get('address'),
             'gender' => session()->get('gender'),
             'isgift' => session()->get('isgift'),
             'payment' => session()->get('payment'),
             'city' => session()->get('city'),
         ];
     }


     $price = Crypt::decrypt($price);
     $order_id = Crypt::decrypt($order_id);
     $url = "https://eu-prod.oppwa.com/v1/checkouts";
     $data = "entityId=8acda4cb89b5ac980189d90f6f1128b8".
             "&amount=$price".
             "&merchantTransactionId=$order_id".
             "&currency=SAR".
             "&paymentType=DB".
             "&customer.email=$user->email".
             "&billing.street1=$user->address".
             "&billing.city=$user->city".
             "&billing.state=$user->city".
             "&billing.country=SA".
             "&billing.postcode=111".
             "&customer.givenName=$user->name".
             "&customer.surname=$user->name";

     $ch = curl_init();
     curl_setopt($ch, CURLOPT_URL, $url);
     curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Authorization:Bearer OGFjOWE0Yzc2N2ExNWMwYjAxNjdkYmYzZDRhYjZmYmJ8ekpycmNNSEY2dw=='));
     curl_setopt($ch, CURLOPT_POST, 1);
     curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
     curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);// this should be set to true in production
     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
     $responseData = curl_exec($ch);
     if(curl_errno($ch)) {
         return curl_error($ch);
     }
     curl_close($ch);
     $hyperpay_checkout_result = json_decode($responseData, true);

    // dd( $hyperpay_checkout_result);

     if($hyperpay_checkout_result['result']['code'] == "000.200.100"){
         $checkoutId = $hyperpay_checkout_result['id'];
         // $shopperResultUrl=$result['result']['code'];
         return view('frontend.appelpaysuccess', compact('checkoutId', 'order_id'));
     }
     else{
         if(Auth::check()){
             HyperPayTransactions::create([
                 "user_id"=>Auth::user()->id,
                 "total"=>$price,
                 "status"=>"ERROR",
                 "data"=>$responseData
             ]);
             return $this->failed(__('alerts.hypercheckouturl'));
         }
         else{
             HyperPayTransactions::create([
                 "user_id"=>26295,
                 "total"=>$price,
                 "status"=>"ERROR",
                 "data"=>$responseData
             ]);
             return $this->failed(__('alerts.hypercheckouturl'));
         }
     }
 }

 public function hyperAppelCardNew($price, $order_id){

    //dd($price, $order_id);
    // echo "<h2>Payment</h2>";
     if(Auth::check()){
         $user = User::find(Auth::user()->id);
     }
     else{
         $user = (object) [
             'id' => '26295',
             'name' => session()->get('name'),
             'email' => session()->get('email'),
             'mobile' => session()->get('mobile'),
             'address' => session()->get('address'),
             'gender' => session()->get('gender'),
             'isgift' => session()->get('isgift'),
             'payment' => session()->get('payment'),
             'city' => session()->get('city'),
         ];
     }

     $price = Crypt::decrypt($price);
     $order_id = Crypt::decrypt($order_id);
     $url = "https://eu-prod.oppwa.com/v1/checkouts";
     $data = "entityId=8acda4c98d598004018d7e26f9b63686".
             "&customParameters[3DS2_enrolled]=true".
             "&amount=$price".
             "&integrity=true".
             "&merchantTransactionId=$order_id".
             "&currency=SAR".
             "&paymentType=DB".
             "&customer.email=$user->email".
             "&billing.street1=$user->address".
             "&billing.city=$user->city".
             "&billing.state=$user->city".
             "&billing.country=SA".
             "&billing.postcode=111".
             "&customer.givenName=$user->name".
             "&customer.surname=$user->name";

     $ch = curl_init();
     curl_setopt($ch, CURLOPT_URL, $url);
     curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Authorization:Bearer OGFjZGE0Yzk4ZDU5ODAwNDAxOGQ3ZTI1MWFlMjM2NTV8cXJZbWJyRFpZdFAzajJSTg=='));
     curl_setopt($ch, CURLOPT_POST, 1);
     curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
     curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);// this should be set to true in production
     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
     $responseData = curl_exec($ch);
     if(curl_errno($ch)) {
         return curl_error($ch);
     }
     curl_close($ch);
     $hyperpay_checkout_result = json_decode($responseData, true);

     //dd( $hyperpay_checkout_result);

     if($hyperpay_checkout_result['result']['code'] == "000.200.100"){

         $checkoutId = $hyperpay_checkout_result['id'];
         $integrity=$hyperpay_checkout_result['integrity'];
         // $shopperResultUrl=$result['result']['code'];
         return view('frontend.appelpaysuccessnew', compact('checkoutId','integrity', 'order_id'));
     }
     else{
         if(Auth::check()){
             HyperPayTransactions::create([
                 "user_id"=>Auth::user()->id,
                 "total"=>$price,
                 "status"=>"ERROR",
                 "data"=>$responseData
             ]);
             return $this->failed(__('alerts.hypercheckouturl'));
         }
         else{
             HyperPayTransactions::create([
                 "user_id"=>26295,
                 "total"=>$price,
                 "status"=>"ERROR",
                 "data"=>$responseData
             ]);
             return $this->failed(__('alerts.hypercheckouturl'));
         }
     }
 }


 public function appelpayresult($id , $order_id){

   // $items = Orders_Items::where('order_id', '=', $order_id)->get();

    $resourcePath = request()->input("resourcePath");
    $url = "https://eu-prod.oppwa.com{$resourcePath}";
	$url .= "?entityId=8acda4cb89b5ac980189d90f6f1128b8";

	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                   'Authorization:Bearer OGFjOWE0Yzc2N2ExNWMwYjAxNjdkYmYzZDRhYjZmYmJ8ekpycmNNSEY2dw=='));
	curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);// this should be set to true in production
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	$responseData = curl_exec($ch);
	if(curl_errno($ch)) {
		return curl_error($ch);
	}
	curl_close($ch);

    $hyperpay_payment_result = json_decode($responseData, true);
    $transaction_result = $hyperpay_payment_result['result']['code'];
    //\Log::channel('applePay')->info("\n$transaction_result");
    //\Log::channel('applePay')->info("\n-------------------------------------------------------");
    $processing_codes = "/^(000\.000\.|000\.100\.1|000\.[36])/";
    $pending_codes = "/^(000\.200)/";
    $pending_v2_codes = "/^(800\.400\.5|100\.400\.500)/";

    $old_transaction = HyperPayTransactions::where('order_id', '=', $order_id)->get();

    if(!$old_transaction->isEmpty()){
        return $this->failed(__('alerts.hyperpaytransaction'));
    }
    else {
        if(preg_match($processing_codes, $transaction_result)){
            $hyperpay_order_details = Orders::find($order_id);
            $hyperpay_order_details->payment_method = 'ApplePay';
            $hyperpay_order_details->status = "Processing";
            $hyperpay_order_details->save();


            // Clear Copoun Session
            if(session()->has('coupon')){
                $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
                $used_coupon->used = $used_coupon->used + 1;
                $used_coupon->save();
                session()->forget('coupon');
            }

            if(Auth::check()){
                $user = User::find(Auth::user()->id);
                // Clear Cart
                Cart::where('user_id', '=', $user->id)->delete();
                $user->isgift = 0;
                $user->giftmessage = "nothing";
                $user->notes = "nothing";
                $user->save();
                HyperPayTransactions::create([
                    "transaction_id"=>$hyperpay_payment_result['id'],
                    "checkout_id"=>$id,
                    "user_id"=>$user->id,
                    "order_id"=>$order_id,
                    "total"=>$hyperpay_order_details->total_price,
                    "status"=>"Processing",
                    "data"=>$responseData,
                ]);
                return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
            }
            else{
                Cart::where('session_id', '=', session()->getId())->delete();
                HyperPayTransactions::create([
                    "transaction_id"=>$hyperpay_payment_result['id'],
                    "checkout_id"=>$id,
                    "user_id"=>26295,
                    "order_id"=>$order_id,
                    "total"=>$hyperpay_order_details->total_price,
                    "status"=>"Processing",
                    "data"=>$responseData,
                ]);
                return redirect()->route('checkout.guestconfirmed', ['id'=>$order_id]);
            }
        }
        else if(preg_match($pending_codes, $transaction_result) || preg_match($pending_v2_codes, $transaction_result)){
            $hyperpay_order_details = Orders::find($order_id);
            $hyperpay_order_details->payment_method = 'ApplePay';
            $hyperpay_order_details->status = "Pending";
            $hyperpay_order_details->save();

            // Clear Copoun Session
            if(session()->has('coupon')){
                $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
                $used_coupon->used = $used_coupon->used + 1;
                $used_coupon->save();
                session()->forget('coupon');
            }
            if(Auth::check()){
                $user = User::find(Auth::user()->id);
                // Clear Cart
                Cart::where('user_id', '=', $user->id)->delete();
                $user->isgift = 0;
                $user->giftmessage = "nothing";
                $user->notes = "nothing";
                $user->save();

                HyperPayTransactions::create([
                    "transaction_id"=>$hyperpay_payment_result['id'],
                    "checkout_id"=>$id,
                    "user_id"=>$user->id,
                    "order_id"=>$order_id,
                    "total"=>$hyperpay_order_details->total_price,
                    "status"=>"Pending",
                    "data"=>$responseData,
                ]);
                return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
            }
            else{
                Cart::where('session_id', '=', session()->getId())->delete();
                HyperPayTransactions::create([
                    "transaction_id"=>$hyperpay_payment_result['id'],
                    "checkout_id"=>$id,
                    "user_id"=>26295,
                    "order_id"=>$order_id,
                    "total"=>$hyperpay_order_details->total_price,
                    "status"=>"Pending",
                    "data"=>$responseData,
                ]);
                return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
            }
        }
        else{
            $hyperpay_order_details = Orders::find($order_id);
            $hyperpay_order_details->payment_method = 'ApplePay';
            $hyperpay_order_details->status = "Rejected";
            $hyperpay_order_details->save();

            //RESTOCK Products
            $pending_products = Orders_Items::where('order_id', '=', $order_id)->get();
            foreach($pending_products as $item){
                $restock_Product = Products::find($item->product_id);
                $restock_Product->stock = $restock_Product->stock + $item->product_qty;
                $restock_Product->save();
            }
            if(Auth::check()){
                $user = User::find(Auth::user()->id);
                HyperPayTransactions::create([
                    "transaction_id"=>$hyperpay_payment_result['id'],
                    "checkout_id"=>$id,
                    "user_id"=>$user->id,
                    "order_id"=>$order_id,
                    "total"=>$hyperpay_order_details->total_price,
                    "status"=>"REJECTED",
                    "data"=>$responseData,
                ]);
                return $this->failed(__('alerts.Your payment has beed REJECTED from the gateway'));
            }
            else{
                HyperPayTransactions::create([
                    "transaction_id"=>$hyperpay_payment_result['id'],
                    "checkout_id"=>$id,
                    "user_id"=>26295,
                    "order_id"=>$order_id,
                    "total"=>$hyperpay_order_details->total_price,
                    "status"=>"REJECTED",
                    "data"=>$responseData,
                ]);
                return $this->failed(__('alerts.Your payment has beed REJECTED from the gateway'));

            }

        }
    }
 }

 public function appelpayresultNew($id , $order_id){

    // $items = Orders_Items::where('order_id', '=', $order_id)->get();

     $resourcePath = request()->input("resourcePath");
     $url = "https://eu-prod.oppwa.com{$resourcePath}";
     $url .= "?entityId=8acda4c98d598004018d7e26f9b63686";

     $ch = curl_init();
     curl_setopt($ch, CURLOPT_URL, $url);
     curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Authorization:Bearer OGFjZGE0Yzk4ZDU5ODAwNDAxOGQ3ZTI1MWFlMjM2NTV8cXJZbWJyRFpZdFAzajJSTg=='));
     curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
     curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);// this should be set to true in production
     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
     $responseData = curl_exec($ch);
     if(curl_errno($ch)) {
         return curl_error($ch);
     }
     curl_close($ch);

     $hyperpay_payment_result = json_decode($responseData, true);
     $transaction_result = $hyperpay_payment_result['result']['code'];
     //\Log::channel('applePayNew')->info("\n$transaction_result");
     //\Log::channel('applePayNew')->info("\n-------------------------------------------------------");
     $processing_codes = "/^(000\.000\.|000\.100\.1|000\.[36])/";
     $pending_codes = "/^(000\.200)/";
     $pending_v2_codes = "/^(800\.400\.5|100\.400\.500)/";

     $old_transaction = HyperPayTransactions::where('order_id', '=', $order_id)->get();

     if(!$old_transaction->isEmpty()){
         return $this->failed(__('alerts.hyperpaytransaction'));
     }
     else {
         if(preg_match($processing_codes, $transaction_result)){
             $hyperpay_order_details = Orders::find($order_id);
             $hyperpay_order_details->payment_method = 'ApplePayNew';
             $hyperpay_order_details->status = "Processing";
             $hyperpay_order_details->save();


             // Clear Copoun Session
             if(session()->has('coupon')){
                 $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
                 $used_coupon->used = $used_coupon->used + 1;
                 $used_coupon->save();
                 session()->forget('coupon');
             }

             if(Auth::check()){
                 $user = User::find(Auth::user()->id);
                 // Clear Cart
                 Cart::where('user_id', '=', $user->id)->delete();
                 $user->isgift = 0;
                 $user->giftmessage = "nothing";
                 $user->notes = "nothing";
                 $user->save();
                 HyperPayTransactions::create([
                     "transaction_id"=>$hyperpay_payment_result['id'],
                     "checkout_id"=>$id,
                     "user_id"=>$user->id,
                     "order_id"=>$order_id,
                     "total"=>$hyperpay_order_details->total_price,
                     "status"=>"Processing",
                     "data"=>$responseData,
                 ]);
                 return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
             }
             else{
                 Cart::where('session_id', '=', session()->getId())->delete();
                 HyperPayTransactions::create([
                     "transaction_id"=>$hyperpay_payment_result['id'],
                     "checkout_id"=>$id,
                     "user_id"=>26295,
                     "order_id"=>$order_id,
                     "total"=>$hyperpay_order_details->total_price,
                     "status"=>"Processing",
                     "data"=>$responseData,
                 ]);
                 return redirect()->route('checkout.guestconfirmed', ['id'=>$order_id]);
             }
         }
         else if(preg_match($pending_codes, $transaction_result) || preg_match($pending_v2_codes, $transaction_result)){
             $hyperpay_order_details = Orders::find($order_id);
             $hyperpay_order_details->payment_method = 'ApplePayNew';
             $hyperpay_order_details->status = "Pending";
             $hyperpay_order_details->save();

             // Clear Copoun Session
             if(session()->has('coupon')){
                 $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
                 $used_coupon->used = $used_coupon->used + 1;
                 $used_coupon->save();
                 session()->forget('coupon');
             }
             if(Auth::check()){
                 $user = User::find(Auth::user()->id);
                 // Clear Cart
                 Cart::where('user_id', '=', $user->id)->delete();
                 $user->isgift = 0;
                 $user->giftmessage = "nothing";
                 $user->notes = "nothing";
                 $user->save();

                 HyperPayTransactions::create([
                     "transaction_id"=>$hyperpay_payment_result['id'],
                     "checkout_id"=>$id,
                     "user_id"=>$user->id,
                     "order_id"=>$order_id,
                     "total"=>$hyperpay_order_details->total_price,
                     "status"=>"Pending",
                     "data"=>$responseData,
                 ]);
                 return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
             }
             else{
                 Cart::where('session_id', '=', session()->getId())->delete();
                 HyperPayTransactions::create([
                     "transaction_id"=>$hyperpay_payment_result['id'],
                     "checkout_id"=>$id,
                     "user_id"=>26295,
                     "order_id"=>$order_id,
                     "total"=>$hyperpay_order_details->total_price,
                     "status"=>"Pending",
                     "data"=>$responseData,
                 ]);
                 return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
             }
         }
         else{
             $hyperpay_order_details = Orders::find($order_id);
             $hyperpay_order_details->payment_method = 'ApplePayNew';
             $hyperpay_order_details->status = "Rejected";
             $hyperpay_order_details->save();

             //RESTOCK Products
             $pending_products = Orders_Items::where('order_id', '=', $order_id)->get();
             foreach($pending_products as $item){
                 $restock_Product = Products::find($item->product_id);
                 $restock_Product->stock = $restock_Product->stock + $item->product_qty;
                 $restock_Product->save();
             }
             if(Auth::check()){
                 $user = User::find(Auth::user()->id);
                 HyperPayTransactions::create([
                     "transaction_id"=>$hyperpay_payment_result['id'],
                     "checkout_id"=>$id,
                     "user_id"=>$user->id,
                     "order_id"=>$order_id,
                     "total"=>$hyperpay_order_details->total_price,
                     "status"=>"REJECTED",
                     "data"=>$responseData,
                 ]);
                 return $this->failed(__('alerts.Your payment has beed REJECTED from the gateway'));
             }
             else{
                 HyperPayTransactions::create([
                     "transaction_id"=>$hyperpay_payment_result['id'],
                     "checkout_id"=>$id,
                     "user_id"=>26295,
                     "order_id"=>$order_id,
                     "total"=>$hyperpay_order_details->total_price,
                     "status"=>"REJECTED",
                     "data"=>$responseData,
                 ]);
                 return $this->failed(__('alerts.Your payment has beed REJECTED from the gateway'));

             }

         }
     }
  }



    public function hyperpaycard($price, $order_id){
        if(Auth::check()){
            $user = User::find(Auth::user()->id);
        }
        else{
            $user = (object) [
                'id' => '26295',
                'name' => session()->get('name'),
                'email' => session()->get('email'),
                'mobile' => session()->get('mobile'),
                'address' => session()->get('address'),
                'gender' => session()->get('gender'),
                'isgift' => session()->get('isgift'),
                'payment' => session()->get('payment'),
                'city' => session()->get('city'),
            ];
        }
        $price = Crypt::decrypt($price);
        $order_id = Crypt::decrypt($order_id);
        $url = "https://eu-prod.oppwa.com/v1/checkouts";
        $data = "entityId=8ac9a4c767a15c0b0167dbf4171c6fbf".
                "&amount=$price".
                "&currency=SAR" .
                "&paymentType=DB" .
                "&merchantTransactionId=$order_id" .
                "&merchant.billing.surname=aDawliah".
                "&merchant.billing.email=<EMAIL>" .
                "&customer.email=$user->email" .
                "&billing.country=SA" .
                "&billing.city=$user->city" .
                "&customer.givenName=" .preg_replace('/[^\w\s]+/u','' , $user->name).
                "&customer.surname= aDawliah" .
                "&billing.street1=".preg_replace('/[^\w\s]+/u','' , $user->address);


        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                       'Authorization:Bearer OGFjOWE0Yzc2N2ExNWMwYjAxNjdkYmYzZDRhYjZmYmJ8ekpycmNNSEY2dw=='));
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);// this should be set to true in production
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $responseData = curl_exec($ch);
        if(curl_errno($ch)) {
            return curl_error($ch);
        }
        curl_close($ch);
        $hyperpay_checkout_result = json_decode($responseData, true);
        // return $hyperpay_checkout_result;
        if($hyperpay_checkout_result['result']['code'] == "000.200.100"){
            $checkoutId=$hyperpay_checkout_result['id'];
            // $shopperResultUrl=$result['result']['code'];
            return view('frontend.hypertestsuccess', compact('checkoutId', 'order_id'));
        }
        else{
            if(Auth::check()){
                HyperPayTransactions::create([
                    "user_id"=>Auth::user()->id,
                    "total"=>$price,
                    "status"=>"ERROR",
                    "data"=>$responseData
                ]);
                return $this->failed(__('alerts.hypercheckouturl'));
            }
            else{
                HyperPayTransactions::create([
                    "user_id"=>26295,
                    "total"=>$price,
                    "status"=>"ERROR",
                    "data"=>$responseData
                ]);
                return $this->failed(__('alerts.hypercheckouturl'));
            }
        }
    }

    public function hyperpay($id, $order_id) {
        $url = "https://eu-prod.oppwa.com/v1/checkouts/$id/payment";
        $url .= "?entityId=8ac9a4c767a15c0b0167dbf4171c6fbf";
        $ch= curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                       'Authorization:Bearer OGFjOWE0Yzc2N2ExNWMwYjAxNjdkYmYzZDRhYjZmYmJ8ekpycmNNSEY2dw=='
                       ));
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);// this should be set to true in production
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $responseData = curl_exec($ch);
        if(curl_errno($ch)) {
            return curl_error($ch);
        }
        curl_close($ch);
        $hyperpay_payment_result = json_decode($responseData, true);

        $transaction_result = $hyperpay_payment_result['result']['code'];
        $processing_codes = "/^(000\.000\.|000\.100\.1|000\.[36])/";
        $pending_codes = "/^(000\.200)/";
        $pending_v2_codes = "/^(800\.400\.5|100\.400\.500)/";

        $old_transaction = HyperPayTransactions::where('order_id', '=', $order_id)->get();
        if(!$old_transaction->isEmpty()){
            return $this->failed(__('alerts.hyperpaytransaction'));
        }
        else {
            if(preg_match($processing_codes, $transaction_result)){
                $hyperpay_order_details = Orders::find($order_id);
                $hyperpay_order_details->payment_method = 'VisaMaster';
                $hyperpay_order_details->status = "Processing";
                $hyperpay_order_details->save();


                // Google Analytics 4 Enhanced Ecommerce
                $items = Orders_Items::where('order_id', '=', $order_id)->get();
                $analytics = new Analytics();
                $client_id = session()->getId();
                if(session()->has('client_id')){
                    $client_id = session()->get('client_id');
                }
                $analytics->setProtocolVersion('1')
                ->setTrackingId('UA-*********-1')
                ->setClientId($client_id);

                // Then, include the transaction data
                $analytics->setTransactionId($hyperpay_order_details->id)
                    ->setRevenue($hyperpay_order_details->total_price)
                    ->setTax($hyperpay_order_details->tax_total)
                    ->setShipping($hyperpay_order_details->shipping_cost);

                foreach($items as $item){
                    $currentproduct = Products::find($item->product_id);
                    // Include a product
                    $productData = [
                        'sku' => $item->product_id,
                        'name' => $item->product_name,
                        'price' => $item->product_final_price,
                        'quantity' => $item->product_qty,
                        'brand' => Brands::find($currentproduct->brand)->title
                    ];
                    $analytics->addProduct($productData);
                }

                // Don't forget to set the product action, in this case to PURCHASE
                $analytics->setProductActionToPurchase();

                // Finally, you must send a hit, in this case we send an Event
                $analytics->setEventCategory('Ecommerce')
                    ->setEventAction('purchase')
                    ->sendEvent();


                // Clear Copoun Session
                if(session()->has('coupon')){
                    $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
                    $used_coupon->used = $used_coupon->used + 1;
                    $used_coupon->save();
                    session()->forget('coupon');
                }

                if(Auth::check()){
                    $user = User::find(Auth::user()->id);
                    // Clear Cart
                    Cart::where('user_id', '=', $user->id)->delete();
                    $user->isgift = 0;
                    $user->giftmessage = "nothing";
                    $user->notes = "nothing";
                    $user->save();
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>$user->id,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Processing",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                }
                else{
                    Cart::where('session_id', '=', session()->getId())->delete();
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>26295,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Processing",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.guestconfirmed', ['id'=>$order_id]);
                }
            }
            else if(preg_match($pending_codes, $transaction_result) || preg_match($pending_v2_codes, $transaction_result)){
                $hyperpay_order_details = Orders::find($order_id);
                $hyperpay_order_details->payment_method = 'VisaMaster';
                $hyperpay_order_details->status = "Pending";
                $hyperpay_order_details->save();

                // Google Analytics 4 Enhanced Ecommerce
                $items = Orders_Items::where('order_id', '=', $order_id)->get();
                $analytics = new Analytics();
                $client_id = session()->getId();
                if(session()->has('client_id')){
                    $client_id = session()->get('client_id');
                }
                $analytics->setProtocolVersion('1')
                ->setTrackingId('UA-*********-1')
                ->setClientId($client_id);

                // Then, include the transaction data
                $analytics->setTransactionId($hyperpay_order_details->id)
                    ->setRevenue($hyperpay_order_details->total_price)
                    ->setTax($hyperpay_order_details->tax_total)
                    ->setShipping($hyperpay_order_details->shipping_cost);

                foreach($items as $item){
                    $currentproduct = Products::find($item->product_id);
                    // Include a product
                    $productData = [
                        'sku' => $item->product_id,
                        'name' => $item->product_name,
                        'price' => $item->product_final_price,
                        'quantity' => $item->product_qty,
                        'brand' => Brands::find($currentproduct->brand)->title
                    ];
                    $analytics->addProduct($productData);
                }

                // Don't forget to set the product action, in this case to PURCHASE
                $analytics->setProductActionToPurchase();

                // Finally, you must send a hit, in this case we send an Event
                $analytics->setEventCategory('Ecommerce')
                    ->setEventAction('purchase')
                    ->sendEvent();


                // Clear Copoun Session
                if(session()->has('coupon')){
                    $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
                    $used_coupon->used = $used_coupon->used + 1;
                    $used_coupon->save();
                    session()->forget('coupon');
                }
                if(Auth::check()){
                    $user = User::find(Auth::user()->id);
                    // Clear Cart
                    Cart::where('user_id', '=', $user->id)->delete();
                    $user->isgift = 0;
                    $user->giftmessage = "nothing";
                    $user->notes = "nothing";
                    $user->save();

                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>$user->id,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Pending",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                }
                else{
                    Cart::where('session_id', '=', session()->getId())->delete();
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>26295,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Pending",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                }
            }
            else{
                $hyperpay_order_details = Orders::find($order_id);
                $hyperpay_order_details->payment_method = 'VisaMaster';
                $hyperpay_order_details->status = "Rejected";
                $hyperpay_order_details->save();

                //RESTOCK Products
                $pending_products = Orders_Items::where('order_id', '=', $order_id)->get();
                foreach($pending_products as $item){
                    $restock_Product = Products::find($item->product_id);
                    $restock_Product->stock = $restock_Product->stock + $item->product_qty;
                    $restock_Product->save();
                }
                if(Auth::check()){
                    $user = User::find(Auth::user()->id);
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>$user->id,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"REJECTED",
                        "data"=>$responseData,
                    ]);
                    return $this->failed(__('alerts.Your payment has beed REJECTED from the gateway'));
                }
                else{
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>26295,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"REJECTED",
                        "data"=>$responseData,
                    ]);
                    return $this->failed(__('alerts.Your payment has beed REJECTED from the gateway'));

                }

            }
        }
    }
    public function newcurl_sms($message_text,$mobile){
        //instanc=instance97437
        //token=xrx4inhiex67w28f
        $params=array(
        'token' => 'ik1ozo2ntdl848f2',
        'to' => $mobile,
        'body' => $message_text
        );
        $curl = curl_init();
        curl_setopt_array($curl, array(
        CURLOPT_URL => "https://api.ultramsg.com/instance113578/messages/chat",
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYHOST => 0,
        CURLOPT_SSL_VERIFYPEER => 0,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => http_build_query($params),
        CURLOPT_HTTPHEADER => array(
            "content-type: application/x-www-form-urlencoded"
        ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

       // if ($err) {
       // echo "cURL Error #:" . $err;
       // } else {
       // echo $response;
       // }
}
 public function hyperpaycardnew($price, $order_id){
        if(Auth::check()){
            $user = User::find(Auth::user()->id);
        }
        else{
            $user = (object) [
                'id' => '26295',
                'name' => session()->get('name'),
                'email' => session()->get('email'),
                'mobile' => session()->get('mobile'),
                'address' => session()->get('address'),
                'gender' => session()->get('gender'),
                'isgift' => session()->get('isgift'),
                'payment' => session()->get('payment'),
                'city' => session()->get('city'),
            ];
        }
        $price = Crypt::decrypt($price);
        $order_id = Crypt::decrypt($order_id);
        $url = "https://eu-prod.oppwa.com/v1/checkouts";
        $data = "entityId=8acda4c98d598004018d7e25a9683662".
                "&amount=$price".
                "&integrity=true".
                "&currency=SAR" .
                "&paymentType=DB" .
                "&merchantTransactionId=$order_id" .
                "&merchant.billing.surname=aDawliah".
                "&merchant.billing.email=<EMAIL>" .
                "&customer.email=$user->email" .
                "&billing.country=SA" .
                "&billing.city=$user->city" .
                "&customer.givenName=" .preg_replace('/[^\w\s]+/u','' , $user->name).
                "&customer.surname= aDawliah" .
                "&billing.street1=".preg_replace('/[^\w\s]+/u','' , $user->address);


        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                       'Authorization:Bearer OGFjZGE0Yzk4ZDU5ODAwNDAxOGQ3ZTI1MWFlMjM2NTV8cXJZbWJyRFpZdFAzajJSTg=='));
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);// this should be set to true in production
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $responseData = curl_exec($ch);
        if(curl_errno($ch)) {
            return curl_error($ch);
        }
        curl_close($ch);
        $hyperpay_checkout_result = json_decode($responseData, true);
        // return $hyperpay_checkout_result;
        if($hyperpay_checkout_result['result']['code'] == "000.200.100"){
            $checkoutId=$hyperpay_checkout_result['id'];
            $integrity=$hyperpay_checkout_result['integrity'];
            // $shopperResultUrl=$result['result']['code'];
            return view('frontend.hypertestsuccessnew', compact('checkoutId', 'integrity','order_id'));
        }
        else{
            if(Auth::check()){
                HyperPayTransactions::create([
                    "user_id"=>Auth::user()->id,
                    "total"=>$price,
                    "status"=>"ERROR",
                    "data"=>$responseData
                ]);
                return $this->failed(__('alerts.hypercheckouturl'));
            }
            else{
                HyperPayTransactions::create([
                    "user_id"=>26295,
                    "total"=>$price,
                    "status"=>"ERROR",
                    "data"=>$responseData
                ]);
                return $this->failed(__('alerts.hypercheckouturl'));
            }
        }
    }

    public function hyperpaynew($id, $order_id) {
        $url = "https://eu-prod.oppwa.com/v1/checkouts/$id/payment";
        $url .= "?entityId=8acda4c98d598004018d7e25a9683662";
        $ch= curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                       'Authorization:Bearer OGFjZGE0Yzk4ZDU5ODAwNDAxOGQ3ZTI1MWFlMjM2NTV8cXJZbWJyRFpZdFAzajJSTg=='
                       ));
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);// this should be set to true in production
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $responseData = curl_exec($ch);
        if(curl_errno($ch)) {
            return curl_error($ch);
        }
        curl_close($ch);
        $hyperpay_payment_result = json_decode($responseData, true);

        $transaction_result = $hyperpay_payment_result['result']['code'];
        $processing_codes = "/^(000\.000\.|000\.100\.1|000\.[36])/";
        $pending_codes = "/^(000\.200)/";
        $pending_v2_codes = "/^(800\.400\.5|100\.400\.500)/";

        $old_transaction = HyperPayTransactions::where('order_id', '=', $order_id)->get();
        if(!$old_transaction->isEmpty()){
            return $this->failed(__('alerts.hyperpaytransaction'));
        }
        else {
            if(preg_match($processing_codes, $transaction_result)){
                $hyperpay_order_details = Orders::find($order_id);
                $hyperpay_order_details->payment_method = 'VisaMasterNew';
                $hyperpay_order_details->status = "Processing";
                $hyperpay_order_details->save();

                // Clear Copoun Session
                if(session()->has('coupon')){
                    $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
                    $used_coupon->used = $used_coupon->used + 1;
                    $used_coupon->save();
                    session()->forget('coupon');
                }

                if(Auth::check()){
                    $user = User::find(Auth::user()->id);
                    // Clear Cart
                    Cart::where('user_id', '=', $user->id)->delete();
                    $user->isgift = 0;
                    $user->giftmessage = "nothing";
                    $user->notes = "nothing";
                    $user->save();
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>$user->id,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Processing",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                }
                else{
                    Cart::where('session_id', '=', session()->getId())->delete();
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>26295,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Processing",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.guestconfirmed', ['id'=>$order_id]);
                }
            }
            else if(preg_match($pending_codes, $transaction_result) || preg_match($pending_v2_codes, $transaction_result)){
                $hyperpay_order_details = Orders::find($order_id);
                $hyperpay_order_details->payment_method = 'VisaMasterNew';
                $hyperpay_order_details->status = "Pending";
                $hyperpay_order_details->save();


                // Clear Copoun Session
                if(session()->has('coupon')){
                    $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
                    $used_coupon->used = $used_coupon->used + 1;
                    $used_coupon->save();
                    session()->forget('coupon');
                }
                if(Auth::check()){
                    $user = User::find(Auth::user()->id);
                    // Clear Cart
                    Cart::where('user_id', '=', $user->id)->delete();
                    $user->isgift = 0;
                    $user->giftmessage = "nothing";
                    $user->notes = "nothing";
                    $user->save();

                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>$user->id,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Pending",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                }
                else{
                    Cart::where('session_id', '=', session()->getId())->delete();
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>26295,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Pending",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                }
            }
            else{
                $hyperpay_order_details = Orders::find($order_id);
                $hyperpay_order_details->payment_method = 'VisaMasterNew';
                $hyperpay_order_details->status = "Rejected";
                $hyperpay_order_details->save();

                //RESTOCK Products
                $pending_products = Orders_Items::where('order_id', '=', $order_id)->get();
                foreach($pending_products as $item){
                    $restock_Product = Products::find($item->product_id);
                    $restock_Product->stock = $restock_Product->stock + $item->product_qty;
                    $restock_Product->save();
                }
                if(Auth::check()){
                    $user = User::find(Auth::user()->id);
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>$user->id,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"REJECTED",
                        "data"=>$responseData,
                    ]);
                    return $this->failed(__('alerts.Your payment has beed REJECTED from the gateway'));
                }
                else{
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>26295,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"REJECTED",
                        "data"=>$responseData,
                    ]);
                    return $this->failed(__('alerts.Your payment has beed REJECTED from the gateway'));

                }

            }
        }
    }



    public function hyperpaymadacard($price, $order_id){
        if(Auth::check()){
            $user = User::find(Auth::user()->id);
        }
        else{
            $user = (object) [
                'id' => '26295',
                'name' => session()->get('name'),
                'email' => session()->get('email'),
                'mobile' => session()->get('mobile'),
                'address' => session()->get('address'),
                'gender' => session()->get('gender'),
                'isgift' => session()->get('isgift'),
                'payment' => session()->get('payment'),
                'city' => session()->get('city'),
            ];
        }
        $price = Crypt::decrypt($price);
        $order_id = Crypt::decrypt($order_id);
        $url = "https://eu-prod.oppwa.com/v1/checkouts";
        $data = "entityId=8acda4cd70c8e89c0170c9242bb502ec".
                "&amount=$price".
                "&merchantTransactionId=$order_id".
                "&currency=SAR".
                "&paymentType=DB";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                       'Authorization:Bearer OGFjOWE0Yzc2N2ExNWMwYjAxNjdkYmYzZDRhYjZmYmJ8ekpycmNNSEY2dw=='));
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);// this should be set to true in production
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $responseData = curl_exec($ch);
        if(curl_errno($ch)) {
            return curl_error($ch);
        }
        curl_close($ch);
        $hyperpay_checkout_result = json_decode($responseData, true);
        if($hyperpay_checkout_result['result']['code'] == "000.200.100"){
            $checkoutId=$hyperpay_checkout_result['id'];
            // $shopperResultUrl=$result['result']['code'];
            return view('frontend.hypertestsuccessmada', compact('checkoutId', 'order_id'));
        }
        else{
            if(Auth::check()){
                HyperPayTransactions::create([
                    "user_id"=>Auth::user()->id,
                    "total"=>$price,
                    "status"=>"ERROR",
                    "data"=>$responseData
                ]);
                return $this->failed(__('alerts.hypercheckouturl'));
            }
            else{
                HyperPayTransactions::create([
                    "user_id"=>26295,
                    "total"=>$price,
                    "status"=>"ERROR",
                    "data"=>$responseData
                ]);
                return $this->failed(__('alerts.hypercheckouturl'));
            }
        }
    }
    public function hyperpaymada($id, $order_id) {
        $url = "https://eu-prod.oppwa.com/v1/checkouts/$id/payment";
        $url .= "?entityId=8acda4cd70c8e89c0170c9242bb502ec";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                       'Authorization:Bearer OGFjOWE0Yzc2N2ExNWMwYjAxNjdkYmYzZDRhYjZmYmJ8ekpycmNNSEY2dw=='
                       ));
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);// this should be set to true in production
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $responseData = curl_exec($ch);
        if(curl_errno($ch)) {
            return curl_error($ch);
        }
        curl_close($ch);
        $hyperpay_payment_result = json_decode($responseData, true);


        $transaction_result = $hyperpay_payment_result['result']['code'];
        $processing_codes = "/^(000\.000\.|000\.100\.1|000\.[36])/";
        $pending_codes = "/^(000\.200)/";
        $pending_v2_codes = "/^(800\.400\.5|100\.400\.500)/";

        $old_transaction = HyperPayTransactions::where('order_id', '=', $order_id)->get();
        if(!$old_transaction->isEmpty()){
            return $this->failed(__('alerts.hyperpaytransaction'));
        }
        else {
            if(preg_match($processing_codes, $transaction_result)){
                $hyperpay_order_details = Orders::find($order_id);
                $hyperpay_order_details->payment_method = 'MADA';
                $hyperpay_order_details->status = "Processing";
                $hyperpay_order_details->save();

                // Google Analytics 4 Enhanced Ecommerce
                $items = Orders_Items::where('order_id', '=', $order_id)->get();
                $analytics = new Analytics();
                $client_id = session()->getId();
                if(session()->has('client_id')){
                    $client_id = session()->get('client_id');
                }
                $analytics->setProtocolVersion('1')
                ->setTrackingId('UA-*********-1')
                ->setClientId($client_id);

                // Then, include the transaction data
                $analytics->setTransactionId($hyperpay_order_details->id)
                    ->setRevenue($hyperpay_order_details->total_price)
                    ->setTax($hyperpay_order_details->tax_total)
                    ->setShipping($hyperpay_order_details->shipping_cost);

                foreach($items as $item){
                    $currentproduct = Products::find($item->product_id);
                    // Include a product
                    $productData = [
                        'sku' => $item->product_id,
                        'name' => $item->product_name,
                        'price' => $item->product_final_price,
                        'quantity' => $item->product_qty,
                        'brand' => Brands::find($currentproduct->brand)->title
                    ];
                    $analytics->addProduct($productData);
                }

                // Don't forget to set the product action, in this case to PURCHASE
                $analytics->setProductActionToPurchase();

                // Finally, you must send a hit, in this case we send an Event
                $analytics->setEventCategory('Ecommerce')
                    ->setEventAction('purchase')
                    ->sendEvent();


                if(session()->has('coupon')){
                    $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
                    $used_coupon->used = $used_coupon->used + 1;
                    $used_coupon->save();
                    session()->forget('coupon');
                }

                if(Auth::check()){
                    $user = User::find(Auth::user()->id);
                    // Clear Cart
                    Cart::where('user_id', '=', $user->id)->delete();

                    // Clear Copoun Session
                    $user->isgift = 0;
                    $user->giftmessage = "nothing";
                    $user->notes = "nothing";
                    $user->save();

                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>$user->id,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Processing",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                }
                else{
                    // Clear Cart
                    Cart::where('session_id', '=', session()->getId())->delete();
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>26295,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Processing",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.guestconfirmed', ['id'=>$order_id]);
                }


            }
            else if(preg_match($pending_codes, $transaction_result) || preg_match($pending_v2_codes, $transaction_result)){
                $hyperpay_order_details = Orders::find($order_id);
                $hyperpay_order_details->payment_method = 'MADA';
                $hyperpay_order_details->status = "Pending";
                $hyperpay_order_details->save();


                // Google Analytics 4 Enhanced Ecommerce
                $items = Orders_Items::where('order_id', '=', $order_id)->get();
                $analytics = new Analytics();
                $client_id = session()->getId();
                if(session()->has('client_id')){
                    $client_id = session()->get('client_id');
                }
                $analytics->setProtocolVersion('1')
                ->setTrackingId('UA-*********-1')
                ->setClientId($client_id);

                // Then, include the transaction data
                $analytics->setTransactionId($hyperpay_order_details->id)
                    ->setRevenue($hyperpay_order_details->total_price)
                    ->setTax($hyperpay_order_details->tax_total)
                    ->setShipping($hyperpay_order_details->shipping_cost);

                foreach($items as $item){
                    $currentproduct = Products::find($item->product_id);
                    // Include a product
                    $productData = [
                        'sku' => $item->product_id,
                        'name' => $item->product_name,
                        'price' => $item->product_final_price,
                        'quantity' => $item->product_qty,
                        'brand' => Brands::find($currentproduct->brand)->title
                    ];
                    $analytics->addProduct($productData);
                }

                // Don't forget to set the product action, in this case to PURCHASE
                $analytics->setProductActionToPurchase();

                // Finally, you must send a hit, in this case we send an Event
                $analytics->setEventCategory('Ecommerce')
                    ->setEventAction('purchase')
                    ->sendEvent();


                // Clear Copoun Session
                if(session()->has('coupon')){
                    $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
                    $used_coupon->used = $used_coupon->used + 1;
                    $used_coupon->save();
                    session()->forget('coupon');
                }

                if(Auth::check()){
                    $user = User::find(Auth::user()->id);
                    // Clear Cart
                    Cart::where('user_id', '=', $user->id)->delete();

                    $user->isgift = 0;
                    $user->giftmessage = "nothing";
                    $user->notes = "nothing";
                    $user->save();
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>$user->id,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Pending",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                }
                else{
                    // Clear Cart
                    Cart::where('session_id', '=', session()->getId())->delete();
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>26295,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Pending",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.guestconfirmed', ['id'=>$order_id]);
                }
            }
            else{
                $hyperpay_order_details = Orders::find($order_id);
                $hyperpay_order_details->payment_method = 'MADA';
                $hyperpay_order_details->status = "REJECTED";
                $hyperpay_order_details->save();

                //RESTOCK Products
                $pending_products = Orders_Items::where('order_id', '=', $order_id)->get();
                foreach($pending_products as $item){
                    $restock_Product = Products::find($item->product_id);
                    $restock_Product->stock = $restock_Product->stock + $item->product_qty;
                    $restock_Product->save();
                }

                if(Auth::check()){
                    $user = User::find(Auth::user()->id);
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>$user->id,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"REJECTED",
                        "data"=>$responseData,
                    ]);
                    return $this->failed(__('alerts.Your payment has beed REJECTED from the gateway'));
                }
                else{
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>26295,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"REJECTED",
                        "data"=>$responseData,
                    ]);
                    return $this->failed(__('alerts.Your payment has beed REJECTED from the gateway'));
                }
            }
        }
    }
    //mht
    public function hyperpaymadacardnew($price, $order_id){
        if(Auth::check()){
            $user = User::find(Auth::user()->id);
        }
        else{
            $user = (object) [
                'id' => '26295',
                'name' => session()->get('name'),
                'email' => session()->get('email'),
                'mobile' => session()->get('mobile'),
                'address' => session()->get('address'),
                'gender' => session()->get('gender'),
                'isgift' => session()->get('isgift'),
                'payment' => session()->get('payment'),
                'city' => session()->get('city'),
            ];
        }
        $price = Crypt::decrypt($price);
        $order_id = Crypt::decrypt($order_id);
        $url = "https://eu-prod.oppwa.com/v1/checkouts";
        $data = "entityId=8acda4c98d598004018d7e2656d53675".
                "&amount=$price".
                "&integrity=true".
                "&merchantTransactionId=$order_id".
                "&currency=SAR".
                "&paymentType=DB";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                       'Authorization:Bearer OGFjZGE0Yzk4ZDU5ODAwNDAxOGQ3ZTI1MWFlMjM2NTV8cXJZbWJyRFpZdFAzajJSTg=='));
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);// this should be set to true in production
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $responseData = curl_exec($ch);
        if(curl_errno($ch)) {
            return curl_error($ch);
        }
        curl_close($ch);
        $hyperpay_checkout_result = json_decode($responseData, true);
        if($hyperpay_checkout_result['result']['code'] == "000.200.100"){
            $checkoutId=$hyperpay_checkout_result['id'];
            $integrity=$hyperpay_checkout_result['integrity'];
            // $shopperResultUrl=$result['result']['code'];
            return view('frontend.hypertestsuccessmadanew', compact('checkoutId','integrity', 'order_id'));
        }
        else{
            if(Auth::check()){
                HyperPayTransactions::create([
                    "user_id"=>Auth::user()->id,
                    "total"=>$price,
                    "status"=>"ERROR",
                    "data"=>$responseData
                ]);
                return $this->failed(__('alerts.hypercheckouturl'));
            }
            else{
                HyperPayTransactions::create([
                    "user_id"=>26295,
                    "total"=>$price,
                    "status"=>"ERROR",
                    "data"=>$responseData
                ]);
                return $this->failed(__('alerts.hypercheckouturl'));
            }
        }
    }
        //mht
        //get result of payment
    public function hyperpaymadanew($id, $order_id) {
        $url = "https://eu-prod.oppwa.com/v1/checkouts/$id/payment";
        $url .= "?entityId=8acda4c98d598004018d7e2656d53675";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                       'Authorization:Bearer OGFjZGE0Yzk4ZDU5ODAwNDAxOGQ3ZTI1MWFlMjM2NTV8cXJZbWJyRFpZdFAzajJSTg=='
                       ));
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);// this should be set to true in production
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $responseData = curl_exec($ch);
        if(curl_errno($ch)) {
            return curl_error($ch);
        }
        curl_close($ch);
        $hyperpay_payment_result = json_decode($responseData, true);


        $transaction_result = $hyperpay_payment_result['result']['code'];
        $processing_codes = "/^(000\.000\.|000\.100\.1|000\.[36])/";
        $pending_codes = "/^(000\.200)/";
        $pending_v2_codes = "/^(800\.400\.5|100\.400\.500)/";

        $old_transaction = HyperPayTransactions::where('order_id', '=', $order_id)->get();
        if(!$old_transaction->isEmpty()){
            return $this->failed(__('alerts.hyperpaytransaction'));
        }
        else {
            if(preg_match($processing_codes, $transaction_result)){
                $hyperpay_order_details = Orders::find($order_id);
                $hyperpay_order_details->payment_method = 'MadaNew';
                $hyperpay_order_details->status = "Processing";
                $hyperpay_order_details->save();



                if(session()->has('coupon')){
                    $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
                    $used_coupon->used = $used_coupon->used + 1;
                    $used_coupon->save();
                    session()->forget('coupon');
                }

                if(Auth::check()){
                    $user = User::find(Auth::user()->id);
                    // Clear Cart
                    Cart::where('user_id', '=', $user->id)->delete();

                    // Clear Copoun Session
                    $user->isgift = 0;
                    $user->giftmessage = "nothing";
                    $user->notes = "nothing";
                    $user->save();

                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>$user->id,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Processing",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                }
                else{
                    // Clear Cart
                    Cart::where('session_id', '=', session()->getId())->delete();
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>26295,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Processing",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.guestconfirmed', ['id'=>$order_id]);
                }


            }
            else if(preg_match($pending_codes, $transaction_result) || preg_match($pending_v2_codes, $transaction_result)){
                $hyperpay_order_details = Orders::find($order_id);
                $hyperpay_order_details->payment_method = 'MadaNew';
                $hyperpay_order_details->status = "Pending";
                $hyperpay_order_details->save();




                // Clear Copoun Session
                if(session()->has('coupon')){
                    $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
                    $used_coupon->used = $used_coupon->used + 1;
                    $used_coupon->save();
                    session()->forget('coupon');
                }

                if(Auth::check()){
                    $user = User::find(Auth::user()->id);
                    // Clear Cart
                    Cart::where('user_id', '=', $user->id)->delete();

                    $user->isgift = 0;
                    $user->giftmessage = "nothing";
                    $user->notes = "nothing";
                    $user->save();
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>$user->id,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Pending",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                }
                else{
                    // Clear Cart
                    Cart::where('session_id', '=', session()->getId())->delete();
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>26295,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"Pending",
                        "data"=>$responseData,
                    ]);
                    return redirect()->route('checkout.guestconfirmed', ['id'=>$order_id]);
                }
            }
            else{
                $hyperpay_order_details = Orders::find($order_id);
                $hyperpay_order_details->payment_method = 'MadaNew';
                $hyperpay_order_details->status = "REJECTED";
                $hyperpay_order_details->save();

                //RESTOCK Products
                $pending_products = Orders_Items::where('order_id', '=', $order_id)->get();
                foreach($pending_products as $item){
                    $restock_Product = Products::find($item->product_id);
                    $restock_Product->stock = $restock_Product->stock + $item->product_qty;
                    $restock_Product->save();
                }

                if(Auth::check()){
                    $user = User::find(Auth::user()->id);
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>$user->id,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"REJECTED",
                        "data"=>$responseData,
                    ]);
                    return $this->failed(__('alerts.Your payment has beed REJECTED from the gateway'));
                }
                else{
                    HyperPayTransactions::create([
                        "transaction_id"=>$hyperpay_payment_result['id'],
                        "checkout_id"=>$id,
                        "user_id"=>26295,
                        "order_id"=>$order_id,
                        "total"=>$hyperpay_order_details->total_price,
                        "status"=>"REJECTED",
                        "data"=>$responseData,
                    ]);
                    return $this->failed(__('alerts.Your payment has beed REJECTED from the gateway'));
                }
            }
        }
    }

   public function tabbycard($price, $user_id, $subtotal, $shipping, $includingtax, $discount, $isgift, $giftcolor, $ispickup, $pickup_city, $pickup_branch){
        $price = Crypt::decrypt($price);
        $subtotal = Crypt::decrypt($subtotal);
        if($isgift){
            if($ispickup){
                $finaltotal = $subtotal - $discount + 35;
            }
            else{
                $finaltotal = $subtotal + $shipping - $discount + 35;
            }
        }
        else{
            if($ispickup){
                $finaltotal = $subtotal - $discount;
            }
            else{
                $finaltotal = $subtotal + $shipping - $discount;
            }
        }

        $discount_code = null;
        if($discount > 0){
            $discount_code = session()->get('coupon');
        }

        $registered_since = '';
        if(Auth::check()){
            $user = User::find($user_id);
            //Creating Order
            $carts = Cart::where('user_id', '=', Auth::user()->id)->get();
            $tabby_products = Cart::where('user_id', '=', Auth::user()->id)->get();
            $registered_since = $user->created_at->format(DateTime::ATOM);
        }
        else{
            $user = (object) [
                'id' => '26295',
                'name' => session()->get('name'),
                'email' => session()->get('email'),
                'mobile' => session()->get('mobile'),
                'address' => session()->get('address'),
                'gender' => session()->get('gender'),
                'isgift' => session()->get('isgift'),
                'payment' => session()->get('payment'),
                'city' => session()->get('city'),
                'notes' => session()->get('notes'),
                'giftmessage' => session()->get('giftmessage'),
            ];
            $carts = Cart::where('session_id', '=', session()->getId())->get();
            $tabby_products = Cart::where('session_id', '=', session()->getId())->get();
            $registered_since = now()->format(DateTime::ATOM);
        }
        // Create Order
        if($isgift){
            if($ispickup){
                Orders::create([
                    "user_id"=>$user->id,
                    "name"=>preg_replace('/[^\w\s]+/u','' , $user->name),
                    "email"=>$user->email,
                    "country"=>"ksa",
                    "city"=>$user->city,
                    "address"=>preg_replace('/[^\w\s]+/u','' , $user->address),
                    "gender"=>$user->gender,
                    "mobile"=>$user->mobile,
                    "subtotal"=>$subtotal,
                    "tax_total"=>$includingtax,
                    "shipping_cost"=>$shipping,
                    "additional_charges"=>"35",
                    "discount"=>$discount,
                    "total_price"=>$finaltotal,
                    "payment_method"=>"Tabby",
                    "notes"=>$user->notes,
                    "status"=>"Pending",
                    "isgift"=>$isgift,
                    "giftcolor"=>$giftcolor,
                    "giftmessage"=>$user->giftmessage,
                    "ispickup"=>$ispickup,
                    "pickup_city"=>$pickup_city,
                    "pickup_branch"=>$pickup_branch,
                    "discount_code"=>$discount_code
                ]);

            }
            else{
                Orders::create([
                    "user_id"=>$user->id,
                    "name"=>preg_replace('/[^\w\s]+/u','' , $user->name),
                    "email"=>$user->email,
                    "country"=>"ksa",
                    "city"=>$user->city,
                    "address"=>preg_replace('/[^\w\s]+/u','' , $user->address),
                    "gender"=>$user->gender,
                    "mobile"=>$user->mobile,
                    "subtotal"=>$subtotal,
                    "tax_total"=>$includingtax,
                    "shipping_cost"=>$shipping,
                    "additional_charges"=>"35",
                    "discount"=>$discount,
                    "total_price"=>$finaltotal,
                    "payment_method"=>"Tabby",
                    "notes"=>$user->notes,
                    "status"=>"Pending",
                    "isgift"=>$isgift,
                    "giftcolor"=>$giftcolor,
                    "giftmessage"=>$user->giftmessage,
                    "discount_code"=>$discount_code
                ]);

            }
        }
        else{
            if($ispickup){
                Orders::create([
                    "user_id"=>$user->id,
                    "name"=>preg_replace('/[^\w\s]+/u','' , $user->name),
                    "email"=>$user->email,
                    "country"=>"ksa",
                    "city"=>$user->city,
                    "address"=>preg_replace('/[^\w\s]+/u','' , $user->address),
                    "gender"=>$user->gender,
                    "mobile"=>$user->mobile,
                    "subtotal"=>$subtotal,
                    "tax_total"=>$includingtax,
                    "shipping_cost"=>$shipping,
                    "additional_charges"=>"0",
                    "discount"=>$discount,
                    "total_price"=>$finaltotal,
                    "payment_method"=>"Tabby",
                    "notes"=>$user->notes,
                    "status"=>"Pending",
                    "ispickup"=>$ispickup,
                    "pickup_city"=>$pickup_city,
                    "pickup_branch"=>$pickup_branch,
                    "discount_code"=>$discount_code
                ]);
            }
            else{
                Orders::create([
                    "user_id"=>$user->id,
                    "name"=>preg_replace('/[^\w\s]+/u','' , $user->name),
                    "email"=>$user->email,
                    "country"=>"ksa",
                    "city"=>$user->city,
                    "address"=>preg_replace('/[^\w\s]+/u','' , $user->address),
                    "gender"=>$user->gender,
                    "mobile"=>$user->mobile,
                    "subtotal"=>$subtotal,
                    "tax_total"=>$includingtax,
                    "shipping_cost"=>$shipping,
                    "additional_charges"=>"0",
                    "discount"=>$discount,
                    "total_price"=>$finaltotal,
                    "payment_method"=>"Tabby",
                    "notes"=>$user->notes,
                    "status"=>"Pending",
                    "discount_code"=>$discount_code
                ]);
            }
        }

        $order_id = 0;
        if(Auth::check()){
        // Adding Products To Orders_Items Table & Minus Stock
        $order_id=Orders::where('user_id', '=', $user->id)->latest()->first()->id;
        }
        else{
        // Adding Products To Orders_Items Table & Minus Stock
        $order_id=Orders::where('user_id', '=', '26295')->where('email', '=', session()->get('email'))->latest()->first()->id;
        }

        foreach($carts as $cart){
            $product=Products::find($cart->product_id);
            $branch_stock = BranchesStock::where('sku', '=', $product->sku)->where('city', '=', $pickup_city)->where('branch_code', '=', $pickup_branch)->latest()->first();
            $product_final_price=0;
            if($product->sale_price==0){
                $product_final_price = $product->regular_price;
            }
            else{
                $product_final_price = $product->sale_price;
            }
            Orders_Items::create([
                "order_id"=>$order_id,
                "product_id"=>$product->id,
                "product_name"=>$product->title,
                "product_qty"=>$cart->qty,
                "product_final_price"=>$product_final_price,
                "product_sku"=>$product->sku,
            ]);

            if($ispickup){
                $branch_stock->stock = $branch_stock->stock - $cart->qty;
                $branch_stock->save();
            }
            else{
                $product->stock = $product->stock - $cart->qty;
                $product->save();
            }
        }

        $correctaddress = preg_replace('/[^\w\s]+/u','' , $user->address);
        $tabbyitems = $this->tabbyitems();

        $url = "https://api.tabby.ai/api/v2/checkout";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                       'Authorization:Bearer pk_07ab9455-7fb3-43ce-b8b7-7c93590ca14b',
                    ));

        curl_setopt($ch, CURLOPT_POST, 1);
        // curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_POSTFIELDS, '{
            "payment":
                {
                    "amount": "'.$price.'",
                    "currency": "SAR",
                    "description": "",
                    "buyer": {
                        "phone":"'.$user->mobile.'",
                        "email":"'.$user->email.'",
                        "name":"'.preg_replace('/[^\w\s]+/u','' , $user->name).'"
                    },
                    "buyer_history": {
                        "registered_since":"'.$registered_since.'"
                    },
                    "order": {
                        "reference_id":"'.$order_id.'",
                        "items":[
                            '.$tabbyitems.'
                        ]
                    },
                    "shipping_address": {
                        "city":"'.$user->city.'",
                        "address":"'.$correctaddress.'"
                    },
                    "meta": {
                        "order_id":"#'.$order_id.'",
                        "customer":"'.$user->id.'"
                    }
                },
            "lang": "en",
            "merchant_code": "SA",
            "merchant_urls": {
                "success":"https://adawliahshop.com/en/checkout/place_order/tabby/confirmation/'.Crypt::encrypt($order_id).'",
                "cancel":"https://adawliahshop.com/en/checkout/place_order/tabby/cancel/'.Crypt::encrypt($order_id).'",
                "failure":"https://adawliahshop.com/en/checkout/place_order/tabby/failure/'.Crypt::encrypt($order_id).'"
            }
        }');




        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);// this should be set to true in production
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $responseData = curl_exec($ch);
        if(curl_errno($ch)) {
            return curl_error($ch);
        }
        curl_close($ch);
        $tabby_checkout_result = json_decode($responseData, true);
        if( isset( $tabby_checkout_result['configuration']['available_products']['installments'][0]['web_url'] ) ){
            return Redirect::to($tabby_checkout_result['configuration']['available_products']['installments'][0]['web_url']);
         }
         else{
            TabbyTransactions::create([
                "order_id"=>$order_id,
                "user_id"=>$user->id,
                "status"=>"ERROR",
                "total"=>$finaltotal,
                "data" => $responseData
            ]);
            return $this->failed(__('alerts.tabbycheckouturl'));
        }
    }
    public function tabbyresultsuccess($order_id){
       // dd('success tabby');
        $order_id = Crypt::decrypt($order_id);
        $payment_id = request()->fullUrl();
        $payment_id = stristr($payment_id, 'payment_id=');
        $payment_id = ltrim($payment_id, 'payment_id');
        $payment_id = ltrim($payment_id, '=');
        // $payment_id = $_GET['payment_id'];

        // Google Analytics 4 Enhanced Ecommerce
        $items = Orders_Items::where('order_id', '=', $order_id)->get();
        $order = Orders::find($order_id);
        $analytics = new Analytics();
        $client_id = session()->getId();
        if(session()->has('client_id')){
            $client_id = session()->get('client_id');
        }
        $analytics->setProtocolVersion('1')
        ->setTrackingId('UA-*********-1')
        ->setClientId($client_id);

        // Then, include the transaction data
        $analytics->setTransactionId($order->id)
            ->setRevenue($order->total_price)
            ->setTax($order->tax_total)
            ->setShipping($order->shipping_cost);

        foreach($items as $item){
            $currentproduct = Products::find($item->product_id);
            // Include a product
            $productData = [
                'sku' => $item->product_id,
                'name' => $item->product_name,
                'price' => $item->product_final_price,
                'quantity' => $item->product_qty,
                'brand' => Brands::find($currentproduct->brand)->title
            ];
            $analytics->addProduct($productData);
        }

        // Don't forget to set the product action, in this case to PURCHASE
        $analytics->setProductActionToPurchase();

        // Finally, you must send a hit, in this case we send an Event
        $analytics->setEventCategory('Ecommerce')
            ->setEventAction('purchase')
            ->sendEvent();


        if(Auth::check()){
            // Clear Cart
            Cart::where('user_id', '=', Auth::user()->id)->delete();
            $user = User::find(Auth::user()->id);
            $user->isgift = 0;
            $user->giftmessage = "nothing";
            $user->notes = "nothing";
            $user->save();
        }
        else{
            // Clear Cart
            Cart::where('session_id', '=', session()->getId())->delete();
        }

        // Clear Copoun Session
        if(session()->has('coupon')){
            $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
            $used_coupon->used = $used_coupon->used + 1;
            $used_coupon->save();
            session()->forget('coupon');
        }
        $old_transaction = TabbyTransactions::where('order_id', '=', $order_id)->get();
            if(!$old_transaction->isEmpty()){
                return $this->failed(__('alerts.tabbytransaction'));
            }
            else {
                $order_details=Orders::find($order_id);
                $tabby_payment_status_result = $this->tabbygetstatus($payment_id);
                if($tabby_payment_status_result['status']=="AUTHORIZED"){
                    //capture
                    $captureresult = $this->tabbycapture($payment_id, $order_details->total_price);
                    if( isset( $captureresult['captures'][0]['id']) ){
                        TabbyTransactions::create([
                            "transaction_id"=>$payment_id,
                            "order_id"=>$order_id,
                            "user_id"=>$order_details->user_id,
                            "status"=>"Captured And Closed",
                            "total"=>$order_details->total_price
                        ]);
                        $order_details->status = "Processing";
                        $order_details->save();
                        if(Auth::check()){
                            return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                        }
                        else{
                            return redirect()->route('checkout.guestconfirmed', ['id'=>$order_id]);
                        }
                    }
                    else{
                        TabbyTransactions::create([
                            "transaction_id"=>$payment_id,
                            "order_id"=>$order_id,
                            "user_id"=>$order_details->user_id,
                            "status"=>"AUTHORIZED",
                            "total"=>$order_details->total_price
                        ]);
                        $order_details->status = "Pending";
                        $order_details->save();
                        if(Auth::check()){
                            return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                        }
                        else{
                            return redirect()->route('checkout.guestconfirmed', ['id'=>$order_id]);
                        }
                    }

                }
                else if($tabby_payment_status_result['status']=="CLOSED"){
                    if( isset( $tabby_payment_status_result['captures'][0]['id']) ){
                        $order_details->status = "Processing";
                        $order_details->save();
                        TabbyTransactions::create([
                            "transaction_id"=>$payment_id,
                            "order_id"=>$order_id,
                            "user_id"=>$order_details->user_id,
                            "status"=>"CLOSED",
                            "total"=>$order_details->total_price
                        ]);
                        if(Auth::check()){
                            return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                        }
                        else{
                            return redirect()->route('checkout.guestconfirmed', ['id'=>$order_id]);
                        }
                    }
                    else{
                        $order_details->status = "Pending";
                        $order_details->save();
                        TabbyTransactions::create([
                            "transaction_id"=>$payment_id,
                            "order_id"=>$order_id,
                            "user_id"=>$order_details->user_id,
                            "status"=>"Closed",
                            "total"=>$order_details->total_price
                        ]);
                        if(Auth::check()){
                            return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                        }
                        else{
                            return redirect()->route('checkout.guestconfirmed', ['id'=>$order_id]);
                        }
                    }
                }
                else{
                    TabbyTransactions::create([
                        "transaction_id"=>$payment_id,
                        "order_id"=>$order_id,
                        "user_id"=>$user->id,
                        "status"=>"Pending",
                        "total"=>$order_details->total_price
                    ]);
                    if(Auth::check()){
                        return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                    }
                    else{
                        return redirect()->route('checkout.guestconfirmed', ['id'=>$order_id]);
                    }
                }
            }
    }
    public function tabbyresultcancel($order_id){
        $order_id = Crypt::decrypt($order_id);
        if(Auth::check()){
            $user = User::find(Auth::user()->id);
            $user->isgift = 0;
            $user->giftmessage = "nothing";
            $user->notes = "nothing";
            $user->save();
        }

        //ReStock
        $pending_products = Orders_Items::where('order_id', '=', $order_id)->get();
        foreach($pending_products as $item){
            $restock_Product = Products::find($item->product_id);
            $restock_Product->stock = $restock_Product->stock + $item->product_qty;
            $restock_Product->save();
        }
        $old_transaction = TabbyTransactions::where('order_id', '=', $order_id)->get();
            if(!$old_transaction->isEmpty()){
                return $this->failed(__('alerts.tabbycancel'));
            }
            else {
                $payment_id = request()->fullUrl();
                $payment_id = ltrim(stristr($payment_id, 'payment_id='), 'payment_id=');
                $order_details=Orders::find($order_id);
                //Update User Data
                $order_details->status = "Cancelled";
                $order_details->save();
                TabbyTransactions::create([
                    "transaction_id"=>$payment_id,
                    "order_id"=>$order_id,
                    "user_id"=>$order_details->user_id,
                    "status"=>"Cancelled",
                    "total"=>$order_details->total_price
                ]);
                return $this->failed(__('alerts.tabbycancel'));
            }

    }
    public function tabbyresultfailure($order_id){
        $order_id = Crypt::decrypt($order_id);
        if(Auth::check()){
            $user = User::find(Auth::user()->id);
            $user->isgift = 0;
            $user->giftmessage = "nothing";
            $user->notes = "nothing";
            $user->save();
        }
        //ReStock
        $pending_products = Orders_Items::where('order_id', '=', $order_id)->get();
        foreach($pending_products as $item){
            $restock_Product = Products::find($item->product_id);
            $restock_Product->stock = $restock_Product->stock + $item->product_qty;
            $restock_Product->save();
        }

        $old_transaction = TabbyTransactions::where('order_id', '=', $order_id)->get();
            if(!$old_transaction->isEmpty()){
                return $this->failed(__('alerts.tabbyfailed'));
            }
            else {
                $payment_id = request()->fullUrl();
                $payment_id = ltrim(stristr($payment_id, 'payment_id='), 'payment_id=');
                $order_details=Orders::find($order_id);
                //Update User Data
                $order_details->status = "Cancelled";
                $order_details->save();
                TabbyTransactions::create([
                    "transaction_id"=>$payment_id,
                    "order_id"=>$order_id,
                    "user_id"=>$order_details->user_id,
                    "status"=>"Failure",
                    "total"=>$order_details->total_price
                ]);
                return $this->failed(__('alerts.tabbyfailed'));
            }

    }


    public function tamaracard($price, $user_id, $subtotal, $shipping, $includingtax, $discount, $isgift, $giftcolor, $ispickup, $pickup_city, $pickup_branch){
        $price = Crypt::decrypt($price);
        $subtotal = Crypt::decrypt($subtotal);
        if($isgift){
            if($ispickup){
                $finaltotal = $subtotal - $discount + 35;
            }
            else{
                $finaltotal = $subtotal + $shipping - $discount + 35;
            }
        }
        else{
            if($ispickup){
                $finaltotal = $subtotal - $discount;
            }
            else{
                $finaltotal = $subtotal + $shipping - $discount;
            }
        }

        $discount_code = null;
        if($discount > 0){
            $discount_code = session()->get('coupon');
        }

        $user = '';
        if(Auth::check()){
            $user = User::find($user_id);
            //Creating Order
            $carts = Cart::where('user_id', '=', Auth::user()->id)->get();
        }
        else{
            $user = (object) [
                'id' => '26295',
                'name' => session()->get('name'),
                'email' => session()->get('email'),
                'mobile' => session()->get('mobile'),
                'address' => session()->get('address'),
                'gender' => session()->get('gender'),
                'isgift' => session()->get('isgift'),
                'payment' => session()->get('payment'),
                'city' => session()->get('city'),
                'notes' => session()->get('notes'),
                'giftmessage' => session()->get('giftmessage'),
            ];
            //Creating Order
            $carts = Cart::where('session_id', '=', session()->getId())->get();
        }
        $tamaraitems = $this->tamaraitems();

        // Create Order
        if($isgift){
            if($ispickup){
                Orders::create([
                    "user_id"=>$user->id,
                    "name"=>preg_replace('/[^\w\s]+/u','' , $user->name),
                    "email"=>$user->email,
                    "country"=>"ksa",
                    "city"=>$user->city,
                    "address"=>preg_replace('/[^\w\s]+/u','' , $user->address),
                    "gender"=>$user->gender,
                    "mobile"=>$user->mobile,
                    "subtotal"=>$subtotal,
                    "tax_total"=>$includingtax,
                    "shipping_cost"=>$shipping,
                    "additional_charges"=>"35",
                    "discount"=>$discount,
                    "total_price"=>$finaltotal,
                    "payment_method"=>"Tamara",
                    "notes"=>$user->notes,
                    "status"=>"Pending",
                    "isgift"=>$isgift,
                    "giftcolor"=>$giftcolor,
                    "giftmessage"=>$user->giftmessage,
                    "ispickup"=>$ispickup,
                    "pickup_city"=>$pickup_city,
                    "pickup_branch"=>$pickup_branch,
                    "discount_code"=>$discount_code
                ]);
            }
            else{
                Orders::create([
                    "user_id"=>$user->id,
                    "name"=>preg_replace('/[^\w\s]+/u','' , $user->name),
                    "email"=>$user->email,
                    "country"=>"ksa",
                    "city"=>$user->city,
                    "address"=>preg_replace('/[^\w\s]+/u','' , $user->address),
                    "gender"=>$user->gender,
                    "mobile"=>$user->mobile,
                    "subtotal"=>$subtotal,
                    "tax_total"=>$includingtax,
                    "shipping_cost"=>$shipping,
                    "additional_charges"=>"35",
                    "discount"=>$discount,
                    "total_price"=>$finaltotal,
                    "payment_method"=>"Tamara",
                    "notes"=>$user->notes,
                    "status"=>"Pending",
                    "isgift"=>$isgift,
                    "giftcolor"=>$giftcolor,
                    "giftmessage"=>$user->giftmessage,
                    "discount_code"=>$discount_code
                ]);
            }
        }
        else{
            if($ispickup){
                Orders::create([
                    "user_id"=>$user->id,
                    "name"=>preg_replace('/[^\w\s]+/u','' , $user->name),
                    "email"=>$user->email,
                    "country"=>"ksa",
                    "city"=>$user->city,
                    "address"=>preg_replace('/[^\w\s]+/u','' , $user->address),
                    "gender"=>$user->gender,
                    "mobile"=>$user->mobile,
                    "subtotal"=>$subtotal,
                    "tax_total"=>$includingtax,
                    "shipping_cost"=>$shipping,
                    "additional_charges"=>"0",
                    "discount"=>$discount,
                    "total_price"=>$finaltotal,
                    "payment_method"=>"Tamara",
                    "notes"=>$user->notes,
                    "status"=>"Pending",
                    "ispickup"=>$ispickup,
                    "pickup_city"=>$pickup_city,
                    "pickup_branch"=>$pickup_branch,
                    "discount_code"=>$discount_code
                ]);
            }
            else{
                Orders::create([
                    "user_id"=>$user->id,
                    "name"=>preg_replace('/[^\w\s]+/u','' , $user->name),
                    "email"=>$user->email,
                    "country"=>"ksa",
                    "city"=>$user->city,
                    "address"=>preg_replace('/[^\w\s]+/u','' , $user->address),
                    "gender"=>$user->gender,
                    "mobile"=>$user->mobile,
                    "subtotal"=>$subtotal,
                    "tax_total"=>$includingtax,
                    "shipping_cost"=>$shipping,
                    "additional_charges"=>"0",
                    "discount"=>$discount,
                    "total_price"=>$finaltotal,
                    "payment_method"=>"Tamara",
                    "notes"=>$user->notes,
                    "status"=>"Pending",
                    "discount_code"=>$discount_code
                ]);
            }
        }



        // Adding Products To Orders_Items Table & Minus Stock
        if(Auth::check()){
            $order_id=Orders::where('user_id', '=', $user->id)->latest()->first()->id;
        }
        else{
            $order_id=Orders::where('user_id', '=', '26295')->where('email', '=', session()->get('email'))->latest()->first()->id;
        }
        foreach($carts as $cart){
            $product=Products::find($cart->product_id);
            $branch_stock = BranchesStock::where('sku', '=', $product->sku)->where('city', '=', $pickup_city)->where('branch_code', '=', $pickup_branch)->latest()->first();
            $product_final_price=0;
            if($product->sale_price==0){
                $product_final_price = $product->regular_price;
            }
            else{
                $product_final_price = $product->sale_price;
            }
            Orders_Items::create([
                "order_id"=>$order_id,
                "product_id"=>$product->id,
                "product_name"=>$product->title,
                "product_qty"=>$cart->qty,
                "product_final_price"=>$product_final_price,
                "product_sku"=>$product->sku,
            ]);

            if($ispickup){
                $branch_stock->stock = $branch_stock->stock - $cart->qty;
                $branch_stock->save();
            }
            else{
                $product->stock = $product->stock - $cart->qty;
                $product->save();
            }
        }

        $correctaddress = preg_replace('/[^\w\s]+/u','' , $user->address);
        $url = "https://api.tamara.co/checkout";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                       'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.************************************************************************************************************************************************************************************************.RNAx4m7zZBIT68B5WdefEGGKse2eDmyyPh_dtx4eX_FmhpQji7jsQL5bNU6d6PdvP7KOdYfJ-88QCE_PS0iIEypm2k8j5851THeexDcJWn_nTuGWsrFqSH28OA6uwjhmENUSQvjtgVy71aYSTW_UCTyNd94jes8N-vpnVuu9bmFjVfruXeeLAh3PeaYjCVU6X5eubncVAWHW0cj03uMWrMqmWdQZiwYF2ZfqFUKQ_UZNc1hpiR6ZhwLiIGAqCfWc0I9J8lnBLXP0ecGnU7Zts6b7m1s-ceHwavT0N5AvF_QJp0v2gJBpoHbo_WUax7D2JIublPUSskkcBWtNGiqMQg',
                    ));

        curl_setopt($ch, CURLOPT_POST, 1);
        // curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_POSTFIELDS, '{
            "order_reference_id": "'.$order_id.'",
            "order_number": "'.$order_id.'",
            "total_amount": {
              "amount": "'.$price.'",
              "currency": "SAR"
            },
            "description": "aDawliah Products",
            "country_code": "SA",
            "payment_type": "PAY_BY_INSTALMENTS",
            "instalments": 3,
            "locale": "en_US",
            "items": [
              '.$tamaraitems.'
            ],
            "consumer": {
              "first_name": "'.preg_replace('/[^\w\s]+/u','' , $user->name).'",
              "last_name": "",
              "phone_number": "'.$user->mobile.'",
              "email": "'.$user->email.'"
            },
            "billing_address": {
                "first_name": "'.preg_replace('/[^\w\s]+/u','' , $user->name).'",
                "last_name": "",
                "line1": "'.$correctaddress.'",
                "city": "'.$user->city.'",
                "country_code": "SA",
                "phone_number": "'.$user->mobile.'"
                },
            "shipping_address": {
              "first_name": "'.preg_replace('/[^\w\s]+/u','' , $user->name).'",
              "last_name": "",
              "line1": "'.$correctaddress.'",
              "city": "'.$user->city.'",
              "country_code": "SA",
              "phone_number": "'.$user->mobile.'"
            },
            "tax_amount": {
              "amount": "'.$includingtax.'",
              "currency": "SAR"
            },
            "shipping_amount": {
              "amount": "'.$shipping.'",
              "currency": "SAR"
            },
            "merchant_url": {
              "success": "https://adawliahshop.com/en/checkout/place_order/tamara/confirmation/'.Crypt::encrypt($order_id).'",
              "failure": "https://adawliahshop.com/en/checkout/place_order/tamara/failure/'.Crypt::encrypt($order_id).'",
              "cancel": "https://adawliahshop.com/en/checkout/place_order/tamara/cancel/'.Crypt::encrypt($order_id).'",
              "notification": "https://adawliahshop.com/en/checkout/place_order/tamara/authorize/'.Crypt::encrypt($order_id).'"
            }
          }
          ');

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);// this should be set to true in production
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $responseData = curl_exec($ch);
        if(curl_errno($ch)) {
            return curl_error($ch);
        }
        curl_close($ch);
        $tamara_checkout_result = json_decode($responseData, true);
        if( isset( $tamara_checkout_result['checkout_url'] ) ){
            return Redirect::to($tamara_checkout_result['checkout_url']);
         }
         else{
            TamaraTransactions::create([
                "order_id"=>$order_id,
                "user_id"=>$user->id,
                "status"=>"ERROR",
                "total"=>$price,
                "data"=>$responseData
            ]);
            return $this->failed(__('alerts.tamaracheckouturl'));
        }
    }
    public function tamararesultsuccess($order_id){
        $order_id = Crypt::decrypt($order_id);

        // Google Analytics 4 Enhanced Ecommerce
        $items = Orders_Items::where('order_id', '=', $order_id)->get();
        $order = Orders::find($order_id);
        $analytics = new Analytics();
        $client_id = session()->getId();
        if(session()->has('client_id')){
            $client_id = session()->get('client_id');
        }
        $analytics->setProtocolVersion('1')
        ->setTrackingId('UA-*********-1')
        ->setClientId($client_id);

        // Then, include the transaction data
        $analytics->setTransactionId($order->id)
            ->setRevenue($order->total_price)
            ->setTax($order->tax_total)
            ->setShipping($order->shipping_cost);

        foreach($items as $item){
            $currentproduct = Products::find($item->product_id);
            // Include a product
            $productData = [
                'sku' => $item->product_id,
                'name' => $item->product_name,
                'price' => $item->product_final_price,
                'quantity' => $item->product_qty,
                'brand' => Brands::find($currentproduct->brand)->title
            ];
            $analytics->addProduct($productData);
        }

        // Don't forget to set the product action, in this case to PURCHASE
        $analytics->setProductActionToPurchase();

        // Finally, you must send a hit, in this case we send an Event
        $analytics->setEventCategory('Ecommerce')
            ->setEventAction('purchase')
            ->sendEvent();


        if(Auth::check()){
            // Clear Cart
            Cart::where('user_id', '=', Auth::user()->id)->delete();
            $user = User::find(Auth::user()->id);
            $user->isgift = 0;
            $user->giftmessage = "nothing";
            $user->notes = "nothing";
            $user->save();
        }
        else{
            // Clear Cart
            Cart::where('session_id', '=', session()->getId())->delete();
        }

        // Clear Copoun Session
        if(session()->has('coupon')){
            $used_coupon = coupon::where('title', '=', session()->get('coupon'))->latest()->first();
            $used_coupon->used = $used_coupon->used + 1;
            $used_coupon->save();
            session()->forget('coupon');
        }

        $old_transaction = TamaraTransactions::where('order_id', '=', $order_id)->get();
            if(!$old_transaction->isEmpty()){
                return $this->failed(__('alerts.tamaratransaction'));
            }
            else {
                // $payment_id = request()->fullUrl();
                // $payment_id = ltrim(stristr($payment_id, 'orderId='), 'orderId=');
                // $payment_id = substr($payment_id, 0, strpos($payment_id, "&paymentStatus="));
                $payment_id = $_GET['orderId'];
                $order_details=Orders::find($order_id);
                //Update User Data
                $order_details->status = "Pending";
                $order_details->save();
                TamaraTransactions::create([
                    "transaction_id"=>$payment_id,
                    "order_id"=>$order_id,
                    "user_id"=>$order_details->user_id,
                    "status"=>"Approved",
                    "total"=>$order_details->total_price
                ]);

                if(Auth::check()){
                    return redirect()->route('checkout.confirmed', ['id'=>$order_id]);
                }
                else{
                    return redirect()->route('checkout.guestconfirmed', ['id'=>$order_id]);
                }
            }

    }
    public function tamararesultcancel($order_id){
        $order_id = Crypt::decrypt($order_id);
        $old_transaction = TamaraTransactions::where('order_id', '=', $order_id)->get();
        if(Auth::check()){
            $user = User::find(Auth::user()->id);
            $user->isgift = 0;
            $user->giftmessage = "nothing";
            $user->notes = "nothing";
            $user->save();
        }
        //ReStock
        $pending_products = Orders_Items::where('order_id', '=', $order_id)->get();
        foreach($pending_products as $item){
            $restock_Product = Products::find($item->product_id);
            $restock_Product->stock = $restock_Product->stock + $item->product_qty;
            $restock_Product->save();
        }

        if(!$old_transaction->isEmpty()){
            return $this->failed(__('alerts.tamaratransaction'));
        }
        else {
            $payment_id = request()->fullUrl();
            $payment_id = ltrim(stristr($payment_id, 'payment_id='), 'payment_id=');
            $order_details=Orders::find($order_id);
            //Update User Data
            $order_details->status = "Cancelled";
            $order_details->save();
            TamaraTransactions::create([
                "transaction_id"=>$payment_id,
                "order_id"=>$order_id,
                "user_id"=>$order_details->user_id,
                "status"=>"Cancel",
                "total"=>$order_details->total_price
            ]);
            return $this->failed(__('alerts.tamaracancel'));
        }

    }
    public function tamararesultfailure($order_id){
        $order_id = Crypt::decrypt($order_id);
        $old_transaction = TamaraTransactions::where('order_id', '=', $order_id)->get();
        if(Auth::check()){
            $user = User::find(Auth::user()->id);
            $user->isgift = 0;
            $user->giftmessage = "nothing";
            $user->notes = "nothing";
            $user->save();
        }
        //ReStock
        $pending_products = Orders_Items::where('order_id', '=', $order_id)->get();
        foreach($pending_products as $item){
            $restock_Product = Products::find($item->product_id);
            $restock_Product->stock = $restock_Product->stock + $item->product_qty;
            $restock_Product->save();
        }
        if(!$old_transaction->isEmpty()){
            return $this->failed(__('alerts.tamaratransaction'));
        }
        else {
            $payment_id = request()->fullUrl();
            $payment_id = ltrim(stristr($payment_id, 'payment_id='), 'payment_id=');
            $order_details=Orders::find($order_id);
            //Update User Data
            $order_details->status = "Cancelled";
            $order_details->save();
            TamaraTransactions::create([
                "transaction_id"=>$payment_id,
                "order_id"=>$order_id,
                "user_id"=>$order_details->user_id,
                "status"=>"Failure",
                "total"=>$order_details->total_price
            ]);
            return $this->failed(__('alerts.tamarafailed'));
        }

    }
    public function tamararesultauthorize($order_id){
        $order_id = Crypt::decrypt($order_id);
        $transaction=TamaraTransactions::where('order_id', '=', $order_id)->latest()->first();
        $url = "https://api.tamara.co/orders/$transaction->transaction_id/authorise";
        $ch = curl_init();
        $data = '{}';
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                       'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.************************************************************************************************************************************************************************************************.RNAx4m7zZBIT68B5WdefEGGKse2eDmyyPh_dtx4eX_FmhpQji7jsQL5bNU6d6PdvP7KOdYfJ-88QCE_PS0iIEypm2k8j5851THeexDcJWn_nTuGWsrFqSH28OA6uwjhmENUSQvjtgVy71aYSTW_UCTyNd94jes8N-vpnVuu9bmFjVfruXeeLAh3PeaYjCVU6X5eubncVAWHW0cj03uMWrMqmWdQZiwYF2ZfqFUKQ_UZNc1hpiR6ZhwLiIGAqCfWc0I9J8lnBLXP0ecGnU7Zts6b7m1s-ceHwavT0N5AvF_QJp0v2gJBpoHbo_WUax7D2JIublPUSskkcBWtNGiqMQg',
                    ));
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);// this should be set to true in production
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $responseData = curl_exec($ch);
        if(curl_errno($ch)) {
            return curl_error($ch);
        }
        curl_close($ch);
        $tamara_authorization_result = json_decode($responseData, true);
        if($tamara_authorization_result['status']=="authorised"){
            $order_status=Orders::find($order_id);
            $order_status->status = "Pending";
            $order_status->save();
            $transaction_status=TamaraTransactions::where("order_id", "=", $order_id)->latest()->first();
            $transaction_status->status = "Authorized";
            $transaction_status->save();
            $capture_result = $this->tamaracapture($order_id);
            if( isset( $capture_result['capture_id'])){
                $order_status->status = "Processing";
                $order_status->save();
                $transaction->data = $capture_result;
                $transaction->status = "Captured";
                $transaction->save();
             }
        }
        else{
            return $responseData;
        }

    }

    public function tamaracapture($id){
        $transaction=TamaraTransactions::where('order_id', '=', $id)->latest()->first();
        if($transaction){
            // URL
        $apiURL = 'https://api.tamara.co/payments/capture';

        // POST Data
        $postInput = [
            'order_id' => ''.$transaction->transaction_id.'',
            'total_amount' => [
                'amount'=>''.$transaction->total.'',
                'currency'=>'SAR',],
            'shipping_info' => [
                'shipped_at'=>''.$transaction->updated_at.'',
                'shipping_company'=>'Zajil',]
        ];

        // Headers
        $headers = [
            //...
            'Content-Type' => 'application/json',
            'Authorization'=> 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.************************************************************************************************************************************************************************************************.RNAx4m7zZBIT68B5WdefEGGKse2eDmyyPh_dtx4eX_FmhpQji7jsQL5bNU6d6PdvP7KOdYfJ-88QCE_PS0iIEypm2k8j5851THeexDcJWn_nTuGWsrFqSH28OA6uwjhmENUSQvjtgVy71aYSTW_UCTyNd94jes8N-vpnVuu9bmFjVfruXeeLAh3PeaYjCVU6X5eubncVAWHW0cj03uMWrMqmWdQZiwYF2ZfqFUKQ_UZNc1hpiR6ZhwLiIGAqCfWc0I9J8lnBLXP0ecGnU7Zts6b7m1s-ceHwavT0N5AvF_QJp0v2gJBpoHbo_WUax7D2JIublPUSskkcBWtNGiqMQg'
        ];

        $response = Http::withHeaders($headers)->post($apiURL, $postInput);

        $statusCode = $response->status();
        $responseBody = json_decode($response->getBody(), true);
        return $responseBody;
        // echo $statusCode;  // status code
        // dd($responseBody); // body response
        }
    }


    public function tamaraitems(){
        if(Auth::check()){
            $carts = Cart::where('user_id', '=', Auth::user()->id)->get();
        }
        else{
            $carts = Cart::where('session_id', '=', session()->getId())->get();
        }
        $carts_count = $carts->count();
        $loop = 0;
        $json = "";
        foreach($carts as $cart){
            $product=Products::find($cart->product_id);
            $total_amount = ($cart->qty) * ($product->regular_price);
            $hyperformat = number_format($total_amount, 2, '.', '');
            $title = preg_replace('/[^\w\s]+/u','' , $product->title);
            $json = $json . "{
                \"reference_id\": \"$product->id\",
                \"type\": \"Physical\",
                \"name\": \"$title\",
                \"sku\": \"$product->sku\",
                \"quantity\": \"$cart->qty\",
                \"total_amount\": {
                    \"amount\": \"$hyperformat\",
                    \"currency\": \"SAR\"
                }
            },";

            $loop = $loop + 1;
            if($carts_count == $loop){
                $json = substr_replace($json ,"",-1);
            }

        }
        return $json;
    }

    public function tabbyitems(){
        if(Auth::check()){
            $carts = Cart::where('user_id', '=', Auth::user()->id)->get();
        }
        else{
            $carts = Cart::where('session_id', '=', session()->getId())->get();
        }
        $carts_count = $carts->count();
        $loop = 0;
        $json = "";
        foreach($carts as $cart){
            $product=Products::find($cart->product_id);
            $product_category = Category::find($product->main_category)->title;
            $total_amount = ($cart->qty) * ($product->regular_price);
            $hyperformat = number_format($total_amount, 2, '.', '');
            $title = preg_replace('/[^\w\s]+/u','' , $product->title);
            $json = $json . "{
                \"title\": \"$title\",
                \"quantity\": $cart->qty,
                \"unit_price\": \"$hyperformat\",
                \"reference_id\": \"$product->sku\",
                \"category\": \"$product_category\"
            },";
            $loop = $loop + 1;
            if($carts_count == $loop){
                $json = substr_replace($json ,"",-1);
            }

        }
        return $json;
    }

    public function confirmed($id){

		$order_email = '<EMAIL>';
		//$finance_email = '<EMAIL>';
        $mohannad = '<EMAIL>';
		//$it_test='<EMAIL>';
        $area_managers = ['00'=>'','01'=>'<EMAIL>','02'=>'<EMAIL>','03'=>'<EMAIL>'];
        $orderowner = Orders::where('id', '=', $id)->where('user_id', '=', Auth::user()->id)->latest()->first();
        $orderproducts = Orders_Items::where('order_id', '=', $id)->get();
        $order_info = $orderowner;
        $products = $orderproducts;
        $user = Auth::user();
        if($orderowner){
            if($orderowner->get_notified == 0){
                if($order_info->ispickup){
                    $branch_info = Branches::where('city', '=', $order_info->pickup_city)->where('code', '=', $order_info->pickup_branch)->latest()->first();
                    $receiveremails = [$order_info->email, $branch_info->email,  $mohannad ,isset($area_managers[$branch_info->city]) ? $area_managers[$branch_info->city] : '' ];
                    Mail::send('email.orderPickupConfirmation', compact('products', 'order_info', 'user','branch_info'), function($message) use($order_info, $receiveremails){
                        $message->to($receiveremails);
                        $message->subject('aDawliah Shop Order Confirmation #'.$order_info->id);
                    });
                    // check mobile length befor sending whatsapp message.
                    // if (length ==12) ->send message
                    // else ->do not send message
                    $customer_mobile =$this->check_mobile_length($order_info->mobile);

                    $text= "Thank You, your order #$order_info->id has been confirmed. Your payment method is: $order_info->payment_method. Please, visit the branch and collect the order";

                    if(strlen($customer_mobile)==12){
                        //sending whatsapp to customer
                        self::newcurl_sms($text,$customer_mobile);
                         //send for it just testing eyad
                        self::newcurl_sms($text,"962788367626");
                    }
                  $text1= "A new order with number #$order_info->id has been placed to pickup from your store ($branch_info->title) using: $order_info->payment_method, Please check your email: $branch_info->email for more details; and customer mobile is:$customer_mobile";
                  $branch_mobile =$this->check_mobile_length($branch_info->mobile);
                  self::newcurl_sms($text1,$branch_mobile);
                    //send ahamad azzab order@adawliah 966540449016
                    // send manager mohannad 966506651858
                    self::newcurl_sms($text1,"966540449016");
                    self::newcurl_sms($text1,"966506651858");
                    //send for it just testing eyad
                    self::newcurl_sms($text1,"962788367626");
                }
                else{
                    $receiveremails = [$order_info->email, $order_email];
                    $customer_mobile =$this->check_mobile_length($order_info->mobile);
                    $text2= "Thank You, your order $order_info->id for $order_info->name has been confirmed. its expected to arrive soon. For any help, Whatsapp us on +966112438957";

                    if(strlen($customer_mobile)==12){
                        //sending whatsapp to customer
                        self::newcurl_sms($text2,$customer_mobile);
                        self::newcurl_sms($text2,"962788367626");
                    }
                    Mail::send('email.orderConfirmation', compact('products', 'order_info', 'user'), function($message) use($order_info, $receiveremails){
                        $message->to($receiveremails);
                        $message->subject('aDawliah Shop Order Confirmation #'.$order_info->id);
                    });
                }
                $orderowner->get_notified = 1;
                $orderowner->save();
            }
            return view('frontend.checkoutconfirmed', compact('orderowner', 'orderproducts'));
        }
        else{
            // Not Allowed
            return redirect()->route('home');
        }
    }
    public function check_mobile_length($mobile){
         // check mobile char length to remove 0 first char and add 966
        // if it is 12 =>ok
        //if it is 9=>add 966
        //if it is 10 => remove 0 first then add 966
        //else wrong mobile number it will not send whats app message
        $customer_mobile=$mobile;
        $mobile_length=strlen($mobile);
        if ($mobile_length ==12){
            $customer_mobile = $mobile;
        }elseif ($mobile_length ==10){
            $customer_mobile = ltrim($mobile, $mobile[0]);
            $customer_mobile = '966'.$customer_mobile;
        }elseif($mobile_length ==9){
            $customer_mobile = '966'.$mobile;
        }
        return $customer_mobile;
    }
    public function guestConfirmed($id){
        $orderowner = Orders::find($id);
        $orderproducts = Orders_Items::where('order_id', '=', $id)->get();
        $order_info = $orderowner;
        $products = $orderproducts;
        $user = (object) [];
        $user->name = session()->get('name');
        $user->email = session()->get('email');
        $user->mobile = session()->get('mobile');
        $user->city = session()->get('city');
        $user->address = session()->get('address');
        $user->gender = session()->get('gender');
        $user->payment = session()->get('payment');
        $user->isgift = session()->get('isgift');
        if($orderowner){
            if($orderowner->get_notified == 0){
                if($order_info->ispickup){
                    $branch_info = Branches::where('city', '=', $order_info->pickup_city)->where('code', '=', $order_info->pickup_branch)->latest()->first();
                    $receiveremails = [$order_info->email, $branch_info->email, '<EMAIL>'];

                    Mail::send('email.orderPickupConfirmation', compact('products', 'order_info', 'user','branch_info'), function($message) use($order_info, $receiveremails){
                        $message->to($receiveremails);
                        $message->subject('aDawliah Shop Order Confirmation #'.$order_info->id);
                    });

                     $customer_mobile = ltrim($order_info->mobile, $order_info->mobile[0]);
                     $text1="Thank You, your order $order_info->id has been confirmed. Your payment method is: $order_info->payment_method. Please, visit the branch and collect the order";
                    Help::send_sms( "966$customer_mobile" , $text1);


					  $text2= "A new order with number #$order_info->id has been placed to pickup from your store ($branch_info->title) using: $order_info->payment_method, Please check your email: $branch_info->email for more details";
					  Help::send_sms(  "966{$branch_info->mobile}" , $text2);

                }
                else{
                    $receiveremails = [$order_info->email, '<EMAIL>'];
                    Mail::send('email.orderConfirmation', compact('products', 'order_info', 'user'), function($message) use($order_info, $receiveremails){
                        $message->to($receiveremails);
                        $message->subject('aDawliah Shop Order Confirmation #'.$order_info->id);
                    });

                     $customer_mobile = ltrim($order_info->mobile, $order_info->mobile[0]);
                     $text3= "Thank You, your order $order_info->id for $order_info->name has been confirmed. its expected to arrive soon. For any help, Whatsapp us on +966114597855";
                    Help::send_sms( "966$customer_mobile" , $text3);
                }
                $orderowner->get_notified = 1;
                $orderowner->save();
            }
            return view('frontend.checkoutconfirmed', compact('orderowner', 'orderproducts'));
        }
        else{
            // Not Allowed
            return redirect()->route('home');
        }
    }

    public function failed($errormessage){
        Alert::error(__('alerts.ERROR!'), '');
        return view('frontend.checkoutfailed', compact('errormessage'));
    }

    public function tabbygetstatus($payment_id){
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.tabby.ai/api/v2/payments/$payment_id");
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer sk_a3c3ee72-2ae6-4517-9a83-df623f7d0932',
         ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
        $result = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Error:' . curl_error($ch);
        }
        curl_close ($ch);
        $result = json_decode($result, true);
        return $result;
    }

    public function tabbycapture($payment_id, $total){
        // $order_details = TabbyTransactions::where('transaction_id', '=', $payment_id)->latest()->first();
        // if($order_details){
            $url2 = "https://api.tabby.ai/api/v1/payments/$payment_id/captures";
            $ch2 = curl_init();
            curl_setopt($ch2, CURLOPT_URL, $url2);
            curl_setopt($ch2, CURLOPT_HTTPHEADER, array(
                        'Authorization: Bearer sk_a3c3ee72-2ae6-4517-9a83-df623f7d0932',
                        ));
            curl_setopt($ch2, CURLOPT_POST, 1);
            // curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch2, CURLOPT_POSTFIELDS, '{
                "amount": "'.$total.'"
            }');
            curl_setopt($ch2, CURLOPT_SSL_VERIFYPEER, false);// this should be set to true in production
            curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
            $responseData = curl_exec($ch2);
            if(curl_errno($ch2)) {
                return curl_error($ch2);
            }
            curl_close($ch2);
            $result = json_decode($responseData, true);
            return $result;
        // }

    }
    public function orderisgift(){
        if(Auth::check()){
            $user = User::find(Auth::user()->id);
            if($user->isgift ==1){
                $user->isgift = 0;
                $user->save();
            }
            else{
                $user->isgift = 1;
                $user->save();
            }
        }
        else{
            if(session()->has('isgift')){
                if(session()->get('isgift') == 0){
                    session()->put('isgift', '1');
                }
                else{
                    session()->put('isgift', '0');
                }
            }
            else{
                session()->put('isgift', '1');
            }
        }
        $success = true;
        $message = "Success!";
        //  return response
        return response()->json([
            'success' => $success,
            'message' => $message,
        ]);
        // return redirect()->route('checkout.index');

    }

    public function pickupindex(){

        $user = Auth::user();
        $carts = Cart::where('user_id', '=', $user->id)->get();

       // if($user->name == 'testorder_4')
      //  \Log::channel('mht')->info("User City:$user->city");

        if(!$carts->isEmpty()){

            return view('frontend.checkoutpickupfirststep', compact('user','carts'));
        }
        else{
            // $products = Products::all();
            // return view('frontend.index',compact('products'));
            return redirect()->route('home');
        }

    }

    public function pickupindexnew(){

        $user = Auth::user();
        $carts = Cart::where('user_id', '=', $user->id)->get();

       // if($user->name == 'testorder_4')
      //  \Log::channel('mht')->info("User City:$user->city");

        if(!$carts->isEmpty()){
            return view('frontend.checkout_pickup', compact('user','carts'));
        }
        else{
            // $products = Products::all();
            // return view('frontend.index',compact('products'));
            return redirect()->route('home');
        }
    }

    public function guestPickupIndex(){
        $user = (object) [
            'name' => '',
            'email' => '',
            'mobile' => '',
            'city' => 'Riyadh',
            'address' => '',
            'gender' => 'Male',
            'payment' => 'MadaNew',
            'isgift' => '0',
            'giftmessage' => '',
            'notes' => '',
        ];
        if(session()->has('name')){
            $user->name = session()->get('name');;
        }
        if(session()->has('email')){
            $user->email = session()->get('email');;
        }
        if(session()->has('mobile')){
            $user->mobile = session()->get('mobile');;
        }
        if(session()->has('city')){
            $user->city = session()->get('city');;
        }
        if(session()->has('address')){
            $user->address = session()->get('address');;
        }
        if(session()->has('gender')){
            $user->gender = session()->get('gender');;
        }
        if(session()->has('giftmessage')){
            $user->giftmessage = session()->get('giftmessage');;
        }
        if(session()->has('notes')){
            $user->notes = session()->get('notes');;
        }

        if(session()->has('payment')){
            $user->payment = session()->get('payment');;
        }
        else{
            session()->put('payment', 'MadaNew');
        }


        if(session()->has('isgift')){
            $user->isgift = session()->get('isgift');;
        }
        else{
            session()->put('isgift', '0');
        }


        $carts = Cart::where('session_id', '=', session()->getId())->get();
        if(!$carts->isEmpty()){
            // Google Analytics 4 Enhanced Ecommerce
            $analytics = new Analytics();
            $client_id = session()->getId();
            if(session()->has('client_id')){
                $client_id = session()->get('client_id');
            }
            $analytics->setProtocolVersion('1')
            ->setTrackingId('UA-*********-1')
            ->setClientId($client_id);

            foreach($carts as $cart_item){
                $product = Products::find($cart_item->product_id);
                $productData = [
                    'sku' => $product->id,
                    'name' => $product->title,
                    'price' => $product->regular_price,
                    'quantity' => $cart_item->qty,
                    'brand' => Brands::find($product->brand)->title
                ];
                $analytics->addProduct($productData);
            }

            // Don't forget to set the product action, in this case to PURCHASE
            $analytics->setProductActionToCheckout();
            // Finally, you must send a hit, in this case we send an Event
            $analytics->setEventCategory('Ecommerce')
                ->setEventAction('begin_checkout')
                ->sendEvent();

            return view('frontend.checkoutguestpickupfirststep', compact('user','carts'));
        }
        else{
            return redirect()->route('home');
        }

    }

}
